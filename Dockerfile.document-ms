# ---------- Builder: install dev deps & build ----------
FROM node:20-alpine AS builder

WORKDIR /usr/src/app

# Copy manifests first for better layer caching
COPY package*.json ./
COPY tsconfig*.json ./
COPY nest-cli.json ./
# workspace package.json for the service
RUN mkdir -p apps
COPY apps/document-ms/package.json apps/document-ms/

# Install with dev deps (needed for webpack/ts-loader during build)
RUN npm ci

# Copy source and build only the document-ms app
COPY apps/document-ms apps/document-ms
RUN npm run build document-ms

# Prune to production dependencies so the runtime image stays small
RUN npm prune --omit=dev


# ---------- Runtime: minimal, with cron/logrotate ----------
FROM node:20-alpine

ARG NODE_ENV=production
ENV NODE_ENV=${NODE_ENV}

WORKDIR /usr/src/app

# Install logrotate and tzdata
RUN apk add --no-cache logrotate tzdata

# Log directory used by the app and logrotate
RUN mkdir -p /usr/src/app/logs

# Logrotate config (expects a file named logrotate.conf in build context)
COPY logrotate.conf /etc/logrotate.d/applogs

# Cron job to rotate daily at midnight
RUN echo "0 0 * * * /usr/sbin/logrotate -f /etc/logrotate.d/applogs" >> /etc/crontabs/root

# Copy production deps and built artifacts from builder
COPY --from=builder /usr/src/app/node_modules ./node_modules
COPY --from=builder /usr/src/app/dist ./dist
COPY --from=builder /usr/src/app/package*.json ./

EXPOSE 3000

# Start cron (background) then start the app
CMD ["sh", "-c", "crond && node dist/apps/document-ms/main"]
