{"name": "aptitest-service", "version": "0.0.1", "description": "", "author": "", "private": true, "license": "UNLICENSED", "scripts": {"build": "nest build", "format": "prettier --write \"apps/**/*.ts\" \"libs/**/*.ts\"", "start": "nest start", "start:dev": "nest start --watch", "start:debug": "concurrently -k \"npm run start:debug:api-gateway\" \"npm run start:debug:user-service\"", "start:docker": "docker compose -f docker-compose.local.yml up -w ", "start:debug:api-gateway": "nest start --debug 0.0.0.0:9210 --watch api-gateway", "start:debug:user-service": "nest start --debug 0.0.0.0:9229 --watch user-service", "start:debug:assessment-engine-ms": "nest start --debug 0.0.0.0:9231 --watch assessment-engine-ms", "start:debug:blob-ms": "nest start --debug 0.0.0.0:9230 --watch blob-ms", "start:prod": "node dist/apps/api-gateway/main", "lint": "eslint \"{src,apps,libs,test}/**/*.ts\" --fix", "test": "jest", "test:watch": "jest --watch", "test:cov": "jest --coverage", "test:debug": "node --inspect-brk -r tsconfig-paths/register -r ts-node/register node_modules/.bin/jest --runInBand", "test:e2e": "jest --config ./apps/api-gateway/test/jest-e2e.json"}, "dependencies": {"@nestjs-modules/mailer": "^2.0.2", "@nestjs/axios": "^4.0.1", "@nestjs/cache-manager": "^3.0.1", "@nestjs/common": "^11.0.9", "@nestjs/config": "^4.0.2", "@nestjs/core": "^11.0.9", "@nestjs/jwt": "^11.0.0", "@nestjs/microservices": "^11.1.5", "@nestjs/platform-express": "^11.1.5", "@nestjs/swagger": "^11.2.0", "@nestjs/typeorm": "^11.0.0", "amqp-connection-manager": "^4.1.14", "amqplib": "^0.10.8", "axios": "^1.11.0", "bcrypt": "^6.0.0", "cache-manager": "^7.1.0", "cache-manager-redis-store": "^3.0.1", "class-transformer": "^0.5.1", "class-validator": "^0.14.2", "concurrently": "^9.2.0", "handlebars": "^4.7.8", "http-status-codes": "^2.3.0", "multer": "^2.0.2", "nestjs-pino": "^4.4.0", "nodemailer": "^7.0.6", "pg": "^8.16.3", "pino-http": "^10.5.0", "pino-pretty": "^13.1.1", "prom-client": "^15.1.3", "rate-limiter-flexible": "^7.2.0", "react-native-toast-message": "^2.3.3", "reflect-metadata": "^0.2.0", "rxjs": "^7.8.1", "typeorm": "^0.3.25"}, "devDependencies": {"@nestjs/cli": "^11.0.2", "@nestjs/schematics": "^11.0.0", "@nestjs/testing": "^11.0.9", "@types/bcrypt": "^6.0.0", "@types/cache-manager-redis-store": "^2.0.4", "@types/express": "^4.17.17", "@types/http-status-codes": "^1.2.0", "@types/jest": "^29.5.2", "@types/multer": "^2.0.0", "@types/node": "^20.3.1", "@types/supertest": "^6.0.0", "@typescript-eslint/eslint-plugin": "^8.0.0", "@typescript-eslint/parser": "^8.0.0", "clinic": "^13.0.0", "eslint": "^8.42.0", "eslint-config-prettier": "^9.0.0", "eslint-plugin-prettier": "^5.0.0", "jest": "^29.5.0", "prettier": "^3.0.0", "source-map-support": "^0.5.21", "supertest": "^7.0.0", "ts-jest": "^29.1.0", "ts-loader": "^9.5.2", "ts-node": "^10.9.2", "tsconfig-paths": "^4.2.0", "typescript": "^5.1.3"}, "jest": {"moduleFileExtensions": ["js", "json", "ts"], "rootDir": ".", "testRegex": ".*\\.spec\\.ts$", "transform": {"^.+\\.(t|j)s$": "ts-jest"}, "collectCoverageFrom": ["**/*.(t|j)s"], "coverageDirectory": "./coverage", "testEnvironment": "node", "roots": ["<rootDir>/apps/", "<rootDir>/libs/"], "moduleNameMapper": {"^@app/shared(|/.*)$": "<rootDir>/libs/shared/src/$1"}}}