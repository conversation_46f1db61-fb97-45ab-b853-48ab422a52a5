import { ExceptionFilter, Catch, ArgumentsHost, HttpException, HttpStatus } from '@nestjs/common';
import { CustomException } from './custom.exception';
import { PinoLogger } from 'nestjs-pino';
import { Response } from 'express';

@Catch()
export class GlobalExceptionHandler implements ExceptionFilter {
  constructor(private logger: PinoLogger) {}
  catch(exception: any, host: ArgumentsHost) {
    const ctx = host.switchToHttp();
    const response = ctx.getResponse<Response>();

    const request = ctx.getRequest();
    const status = exception instanceof HttpException ? exception.getStatus() : HttpStatus.INTERNAL_SERVER_ERROR;

    response.status(status);

    if (exception && typeof exception.message === 'string' && typeof exception?.status === 'number') {
      response.status(exception.status);
      response.json({
        status: -1,
        message: exception.message,
      });
      return;
    } else if (exception instanceof CustomException) {
      response.status(exception.httpStatus);
      response.json({
        status: -1,
        message: exception.message,
      });
      return;
    }

    const message = 'Error occurred please try again or contact support';
    this.logger.error('request path', request.url, exception);
    response.json({
      status: -1,
      message,
      error: exception,
    });
  }
}
