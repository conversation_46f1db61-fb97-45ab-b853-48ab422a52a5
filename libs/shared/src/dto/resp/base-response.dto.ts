export class BaseResponseDto {
  status: number;
  message: string;

  constructor(status: number = 0, message: string = 'Success') {
    this.status = status;
    this.message = message;
  }
  static success(message: string = 'Success'): BaseResponseDto {
    return new BaseResponseDto(0, message);
  }
}

export class ResponseDto<T> extends BaseResponseDto {
  data: T;

  constructor(data: T);
  constructor(status: number, message: string, data: T);
  constructor(statusOrData: number | T, message?: string, data?: T) {
    super(typeof statusOrData === 'number' ? statusOrData : 0, message ?? 'Success');
    if (typeof statusOrData !== 'number') {
      // First overload: constructor(data: T)
      this.status = 0;
      this.message = 'Success';
      this.data = statusOrData;
    } else {
      // Second overload: constructor(status: number, message: string, data: T)
      this.status = statusOrData;
      this.message = message ?? '';
      this.data = data!;
    }
  }
}
