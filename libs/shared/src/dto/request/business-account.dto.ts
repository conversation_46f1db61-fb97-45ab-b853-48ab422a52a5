import { ApiProperty } from '@nestjs/swagger';
import { IsEmail, IsNotEmpty, IsOptional, IsString } from 'class-validator';

export class BusinessAccountDemo {
  @ApiProperty({ description: 'Name of the business' })
  @IsNotEmpty()
  @IsString()
  name: string;
  @ApiProperty({ description: 'Email of the business' })
  @IsNotEmpty()
  @IsString()
  @IsEmail()
  email: string;
  @ApiProperty({ description: 'Phone number of the business' })
  @IsNotEmpty()
  @IsString()
  phoneNumber: string;
  @ApiProperty({ description: 'Address of the business' })
  @IsNotEmpty()
  @IsString()
  address: string;
  @ApiProperty({ description: 'City of the business' })
  @IsNotEmpty()
  @IsString()
  city: string;
  @ApiProperty({ description: 'Country of the business' })
  @IsNotEmpty()
  @IsString()
  country: string;
}

export class ThemeData {
  @ApiProperty({ description: 'Primary color of the business' })
  @IsNotEmpty()
  @IsString()
  primaryColor: string;
  @ApiProperty({ description: 'Secondary color of the business' })
  @IsNotEmpty()
  @IsString()
  secondaryColor: string;

  @ApiProperty({ description: 'Logo of the business', required: false })
  @IsOptional()
  @IsString()
  logoBase64: string;
}

export class SubscriptionData {
  @ApiProperty({ description: 'Plan of the business' })
  @IsNotEmpty()
  @IsString()
  plan: string;
  startDate: Date;
  endDate: Date;
  isActive: boolean;
  subscriptionId: string;
}

export class BusinessAccountCreation {
  @ApiProperty({ description: 'Business credentials' })
  @IsNotEmpty()
  businessCredentials: BusinessAccountDemo;

  @ApiProperty({ description: 'Subscription data' })
  @IsNotEmpty()
  subscription: SubscriptionData;

  @ApiProperty({ description: 'Theme data' })
  @IsNotEmpty()
  themeData: ThemeData;
}
