import { ApiProperty } from '@nestjs/swagger';
import { IsEmail, IsNotEmpty, IsNumber, IsOptional, IsString } from 'class-validator';

export class CreateBusinessUserDto {
  @ApiProperty({ description: 'ID of the business the user belongs to' })
  @IsNumber()
  businessId: number;

  @ApiProperty({ description: 'First name of the user' })
  @IsString()
  @IsNotEmpty()
  firstName: string;

  @ApiProperty({ description: 'Last name of the user' })
  @IsString()
  @IsNotEmpty()
  lastName: string;

  @ApiProperty({ description: 'Email address of the user' })
  @IsEmail()
  @IsNotEmpty()
  email: string;

  @ApiProperty({ description: 'Password for the user' })
  @IsString()
  @IsNotEmpty()
  password: string;
}

export class UpdateBusinessUserDto {
  @ApiProperty({ description: 'ID of the business the user belongs to' })
  @IsNumber()
  businessId: number;

  @ApiProperty({ description: 'ID of the user to update' })
  @IsNumber()
  userId: number;

  @ApiProperty({ description: 'First name of the user', required: false })
  @IsOptional()
  @IsString()
  firstName?: string;

  @ApiProperty({ description: 'Last name of the user', required: false })
  @IsOptional()
  @IsString()
  lastName?: string;

  @ApiProperty({ description: 'Email address of the user', required: false })
  @IsOptional()
  @IsEmail()
  email?: string;

  @ApiProperty({ description: 'Password for the user', required: false })
  @IsOptional()
  @IsString()
  password?: string;
}

export class BusinessUserIdDto {
  @ApiProperty({ description: 'ID of the business the user belongs to' })
  @IsNumber()
  businessId: number;

  @ApiProperty({ description: 'ID of the user' })
  @IsNumber()
  userId: number;
}
