import { Injectable } from '@nestjs/common';
import { JwtService } from '@nestjs/jwt';
import { RedisCacheService } from '../../cache/redis-cache.service';
import { ConfigService } from '@nestjs/config';
import { PinoLogger } from 'nestjs-pino';
import { CustomException } from '../../exception/custom.exception';
import { HttpService } from '@nestjs/axios';
import { firstValueFrom } from 'rxjs';

@Injectable()
export class AuthService {
  private readonly BLACKLIST_PREFIX = 'token:blacklist:';

  constructor(
    private readonly redisCacheService: RedisCacheService,
    private readonly jwtService: JwtService,
    private readonly configService: ConfigService,
    private readonly logger: PinoLogger,
    private readonly httpService: HttpService
  ) {}

  generateToken(payload: any, refreshToken?: boolean): string {
    const expiresIn = refreshToken
      ? this.configService.get<number>('JWT_REFRESH_EXPIRES_IN', 3600)
      : this.configService.get<number>('JWT_EXPIRES_IN', 3600);

    return this.jwtService.sign(payload, {
      secret: this.configService.get<string>('JWT_SECRET'),
      expiresIn: expiresIn,
    });
  }

  verifyToken(token: string): any {
    return this.jwtService.verify(token, {
      secret: this.configService.get<string>('JWT_SECRET'),
    });
  }

  async isTokenInvalidated(token: string): Promise<boolean> {
    try {
      const decoded = this.jwtService.verify(token, {
        secret: this.configService.get<string>('JWT_SECRET'),
      });

      if (!decoded || typeof decoded !== 'object') {
        return true;
      }

      const blacklisted = await this.redisCacheService.get(`${this.BLACKLIST_PREFIX}${token}`);
      return !!blacklisted;
    } catch (error) {
      this.logger.error('Error occurred while verifying token: ', error);
      return true;
    }
  }

  async invalidateToken(token: string): Promise<void> {
    try {
      const verified = this.jwtService.verify(token, {
        secret: this.configService.get<string>('JWT_SECRET'),
      });

      if (!verified || typeof verified !== 'object' || !('exp' in verified)) {
        throw new Error('Invalid token: missing expiration claim');
      }

      const expirationTime = verified.exp * 1000;
      const now = Date.now();
      const ttl = Math.max(0, Math.ceil((expirationTime - now) / 1000));

      if (ttl > 0) {
        await this.redisCacheService.set(`${this.BLACKLIST_PREFIX}${token}`, 'true', ttl);
      }
    } catch (error) {
      this.logger.error('Error occurred while invalidating token: ', error);
      throw error;
    }
  }

  async verifyGoogleToken(token: string): Promise<any> {
    try {
      const clientId = this.configService.get<string>('GOOGLE_CLIENT_ID');

      const response = await firstValueFrom(
        this.httpService.get(`https://www.googleapis.com/oauth2/v3/tokeninfo?id_token=${token}`)
      );

      if (!response.data) {
        throw new Error('Invalid token');
      }
      const payload = await response.data;
      // if (payload.aud !== clientId) {
      //   throw new Error('Token audience mismatch');
      // }
      return payload;
    } catch (error) {
      this.logger.error('Error occurred while verifying Google token: ', error);
      throw new CustomException('Invalid Google token');
    }
  }
}
