import { Modu<PERSON> } from '@nestjs/common';
import { RateLimitGuard } from './guard/rate-limit.guard';
import { CacheModule } from '../cache/cache.module';
import { JwtService } from '@nestjs/jwt';
import { ConfigModule } from '@nestjs/config';
import { AuthService } from './services/auth.lib.service';
import { HttpModule } from '@nestjs/axios';

@Module({
  imports: [CacheModule, ConfigModule, HttpModule],
  providers: [RateLimitGuard, JwtService, AuthService],
  exports: [RateLimitGuard, JwtService, AuthService],
})
export class AuthModule {}
