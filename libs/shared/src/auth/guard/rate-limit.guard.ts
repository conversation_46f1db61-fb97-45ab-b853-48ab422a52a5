import { CanActivate, ExecutionContext, Injectable, Inject } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { Cache } from 'cache-manager';
import { Request } from 'express';
import { RateLimiterRedis } from 'rate-limiter-flexible';
import { CACHE_MANAGER } from '@nestjs/cache-manager';

@Injectable()
export class RateLimitGuard implements CanActivate {
  private rateLimiter: RateLimiterRedis;

  constructor(
    @Inject(CACHE_MANAGER) private cacheManager: Cache,
    private configService: ConfigService
  ) {
    // use the cache library
    this.rateLimiter = new RateLimiterRedis({
      storeClient: cacheManager,
      points: configService.get('RATE_LIMIT_POINTS') || 10,
      duration: configService.get('RATE_LIMIT_DURATION') || 1,
      blockDuration: configService.get('RATE_LIMIT_BLOCK_DURATION') || 60,
      keyPrefix: 'rate_limit',
    });
  }

  async canActivate(context: ExecutionContext): Promise<boolean> {
    const request = context.switchToHttp().getRequest<Request>();
    const ip = request.ip;
    try {
      await this.rateLimiter.consume(ip);
      return true;
    } catch (error) {
      return false;
    }
  }
}
