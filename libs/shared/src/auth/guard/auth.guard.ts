import { CanActivate, ExecutionContext, Injectable } from '@nestjs/common';
import { JwtService } from '@nestjs/jwt';
import { Request } from 'express';
import { IS_PUBLIC_KEY, BLACKLISTED_JWT_KEY } from '../../util/constant';
import { Reflector } from '@nestjs/core';
import { RedisCacheService } from '../../cache/redis-cache.service';
import { ConfigService } from '@nestjs/config';
import { AuthService } from '../services/auth.lib.service';

@Injectable()
export class AuthGuard implements CanActivate {
  constructor(
    private authService: AuthService,
    private reflector: Reflector,
    private cacheService: RedisCacheService,
    private readonly configService: ConfigService
  ) {}
  async canActivate(context: ExecutionContext): Promise<boolean> {
    const isPublic = this.reflector.getAllAndOverride<boolean>(IS_PUBLIC_KEY, [
      context.getHandler(),
      context.getClass(),
    ]);
    if (isPublic) {
      return true;
    }
    const request = context.switchToHttp().getRequest();
    const { path } = request;
    const publicPaths = ['/health', '/api/metrics']; // Add more if needed
    if (path.includes('/health') || publicPaths.includes(path)) {
      return true;
    }

    const token = request.headers.authorization?.split(' ')[1];
    if (!token) {
      return false;
    }
    try {
      const isInValid = await this.authService.isTokenInvalidated(token);

      request['user'] = await this.authService.verifyToken(token);

      return !isInValid;
    } catch (error) {
      return false;
    }
  }
  private extractTokenFromHeader(request: Request): string | undefined {
    const [type, token] = request.headers.authorization?.split(' ') ?? [];
    return type === 'Bearer' ? token : undefined;
  }
}
