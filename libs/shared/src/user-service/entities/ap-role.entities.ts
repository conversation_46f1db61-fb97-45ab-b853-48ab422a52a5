import { Column, <PERSON>tity, JoinTable, ManyToMany, PrimaryGeneratedColumn } from 'typeorm';
import { BaseEntity } from '../../database/base.entity';
import { ApPrivilege } from './ap-privilege.entities';
import { User } from './user.entities';

@Entity({ name: 'APTI_ROLE' })
export class ApRole extends BaseEntity<ApRole> {
  @Column({ nullable: false, name: 'role_name', type: 'varchar', length: 255, unique: true })
  roleName: string;

  @ManyToMany(() => ApPrivilege, { lazy: true })
  @JoinTable({
    name: 'APTI_ROLE_PRIVILEGE',
    joinColumn: { name: 'role_id' },
    inverseJoinColumn: { name: 'privilege_id' },
  })
  privileges: ApPrivilege[];
}
