import { Column, Entity, JoinTable, ManyToMany, PrimaryGeneratedColumn } from 'typeorm';
import { BaseEntity } from '../../database/base.entity';
import { ApRole } from './ap-role.entities';

@Entity({ name: 'APTI_USER' })
export class User extends BaseEntity<User> {
  @Column({ nullable: false, name: 'first_name', type: 'varchar', length: 255 })
  firstName: string;

  @Column({ nullable: false, name: 'last_name', type: 'varchar', length: 255 })
  lastName: string;

  @Column({ unique: true, nullable: false, name: 'email', type: 'varchar', length: 255 })
  email: string;

  @Column({ nullable: false, name: 'password', type: 'varchar', length: 255 })
  password: string;

  @Column({ nullable: true, name: 'business_id', type: 'int' })
  businessId: number;

  @ManyToMany(() => ApRole, { lazy: true })
  @JoinTable({ name: 'APTI_USER_ROLE', joinColumn: { name: 'user_id' }, inverseJoinColumn: { name: 'role_id' } })
  roles: ApRole[];
}
