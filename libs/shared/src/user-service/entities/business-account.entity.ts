import { Column, Entity, PrimaryGeneratedColumn } from 'typeorm';
import { BaseEntity } from '../../database/base.entity';

@Entity({ name: 'APTI_BUSINESS_ACCOUNT' })
export class BusinessAccount extends BaseEntity<BusinessAccount> {
  @Column({ nullable: false, name: 'name', type: 'varchar', length: 255 })
  name: string;
  @Column({ nullable: false, name: 'email', type: 'varchar', length: 255 })
  email: string;
  @Column({ nullable: false, name: 'phone_number', type: 'varchar', length: 255 })
  phoneNumber: string;
  @Column({ nullable: false, name: 'address', type: 'varchar', length: 255 })
  address: string;
  @Column({ nullable: false, name: 'city', type: 'varchar', length: 255 })
  city: string;
  @Column({ nullable: false, name: 'country', type: 'varchar', length: 255 })
  country: string;
  @Column({ nullable: false, name: 'primary_color', type: 'varchar', length: 255 })
  primaryColor: string;
  @Column({ nullable: false, name: 'secondary_color', type: 'varchar', length: 255 })
  secondaryColor: string;
  @Column({ nullable: true, name: 'logo', type: 'varchar', length: 255 })
  logo: string;
  @Column({ nullable: false, name: 'plan', type: 'varchar', length: 255 })
  plan: string;
  @Column({ nullable: false, name: 'start_date', type: 'date' })
  startDate: Date;
  @Column({ nullable: false, name: 'end_date', type: 'date' })
  endDate: Date;
  @Column({ nullable: false, name: 'is_active', type: 'boolean' })
  isActive: boolean;
  @Column({ nullable: false, name: 'subscription_id', type: 'varchar', length: 255 })
  subscriptionId: string;
}
