import { Column, <PERSON><PERSON>ty, ManyToMany, PrimaryGeneratedColumn } from 'typeorm';
import { BaseEntity } from '../../database/base.entity';
import { ApRole } from './ap-role.entities';

@Entity({ name: 'APTI_PRIVILEGE' })
export class ApPrivilege extends BaseEntity<ApPrivilege> {
  @Column({ nullable: false, name: 'privilege', type: 'varchar', length: 255, unique: true })
  name: string;

  @Column({ nullable: false, name: 'description', type: 'varchar', length: 255 })
  description: string;
}
