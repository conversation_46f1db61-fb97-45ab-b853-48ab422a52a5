import { EntityManager, Repository } from 'typeorm';
import { User } from '../entities/user.entities';
import { BaseRepository } from '../../database/base.repository';
import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';

@Injectable()
export class UserRepository extends BaseRepository<User> {
  constructor(
    protected readonly entityManager: EntityManager,
    @InjectRepository(User)
    protected readonly repository: Repository<User>
  ) {
    super(entityManager, repository);
  }
}
