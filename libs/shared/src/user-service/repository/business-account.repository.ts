import { EntityManager, Repository } from 'typeorm';
import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { BaseRepository } from '../../database/base.repository';
import { BusinessAccount } from '../entities/business-account.entity';

@Injectable()
export class BusinessAccountRepository extends BaseRepository<BusinessAccount> {
  constructor(
    protected readonly entityManager: EntityManager,
    @InjectRepository(BusinessAccount)
    protected readonly repository: Repository<BusinessAccount>
  ) {
    super(entityManager, repository);
  }
}
