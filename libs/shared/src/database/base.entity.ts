import { Column, CreateDateColumn, PrimaryColumn, PrimaryGeneratedColumn, UpdateDateColumn } from 'typeorm';

export abstract class BaseEntity<T> {
  @PrimaryGeneratedColumn({ name: 'id' })
  id: number;

  @Column({
    default: true,
    nullable: false,
    name: 'active',
  })
  active: boolean;

  @Column({
    nullable: true,
    name: 'deleted',
  })
  deleted: boolean;

  @CreateDateColumn({ name: 'created_at', nullable: false })
  createdAt: Date;

  @UpdateDateColumn({ name: 'updated_at', nullable: false })
  updatedAt: Date;

  constructor(data: Partial<T>) {
    Object.assign(this, data);
    this.active = true;
    this.deleted = false;
  }
}
