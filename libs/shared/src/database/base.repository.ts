import { Deep<PERSON>artial, EntityManager, Repository } from 'typeorm';
import { BaseEntity } from './base.entity';

export abstract class BaseRepository<T extends BaseEntity<T>> {
  constructor(
    protected readonly entityManager: EntityManager,
    protected readonly repository: Repository<T>
  ) {}

  getEntityManager(): EntityManager {
    return this.entityManager;
  }

  async findOneBy(payload: any): Promise<T | undefined> {
    return this.repository.findOne({ where: payload });
  }

  async findOne(payload: any): Promise<T | undefined> {
    return this.repository.findOne({ where: payload });
  }

  async createEntity(data: DeepPartial<T>): Promise<T> {
    await this.repository.save(data);
    return data as T;
  }

  async updateEntity(id: number, data: DeepPartial<T>): Promise<T> {
    this.entityManager.update(this.repository.metadata.target, id, data);
    return data as T;
  }

  async deleteEntity(id: number): Promise<void> {
    await this.repository.delete(id);
  }

  async findAll(payload: any): Promise<T[]> {
    return this.repository.find({ where: payload });
  }
}
