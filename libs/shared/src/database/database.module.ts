import { DynamicModule, Module, Logger } from '@nestjs/common';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { TypeOrmModule, TypeOrmModuleOptions } from '@nestjs/typeorm';
import { EntityClassOrSchema } from '@nestjs/typeorm/dist/interfaces/entity-class-or-schema.type';

@Module({
  imports: [
    TypeOrmModule.forRootAsync({
      imports: [ConfigModule],

      useFactory: (configService: ConfigService): TypeOrmModuleOptions => {
        const logger = new Logger(DatabaseModule.name);
        logger.debug('DB_TYPE', configService.get('DB_TYPE'), configService.get<string>('DB_PASSWORD'));
        return {
          type: configService.get<'postgres' | 'mysql' | 'oracle'>('DB_TYPE'),
          host: configService.get<string>('DB_HOST'),
          port: configService.get<number>('DB_PORT'),
          autoLoadEntities: configService.get<boolean>('DATABASE_AUTOLOAD_ENTITIES', false),
          username: configService.get<string>('DB_USERNAME'),
          password: configService.get<string>('DB_PASSWORD'),
          database: configService.get<string>('DB_DATABASE'),
          synchronize: configService.get<boolean>('DB_SYNCHRONIZE', false),
          logging: configService.get<boolean>('DB_LOGGING', false),
          extra: {
            poolMax: configService.get<number>('DB_POOL_MAX', 30),
            poolMin: configService.get<number>('DB_POOL_MIN', 10),
            poolIdleTimeout: configService.get<number>('DB_POOL_IDLE_TIMEOUT', 40000),
          },
        };
      },
      inject: [ConfigService],
    }),
  ],
  exports: [TypeOrmModule],
})
export class DatabaseModule {
  static forFeature(entities: EntityClassOrSchema[]): DynamicModule {
    return {
      module: DatabaseModule,
      imports: [TypeOrmModule.forFeature(entities)],
      exports: [TypeOrmModule],
    };
  }
}
