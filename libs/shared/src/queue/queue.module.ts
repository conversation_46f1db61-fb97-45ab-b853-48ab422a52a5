import { Module } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { ClientsModule, Transport } from '@nestjs/microservices';
import { NotificationQueueService } from './notification-queue.service';
import { AssessmentEngineQueueService } from './assessment-engine-queue.service';
import { QueueName } from '../util/QueueName';

@Module({
  imports: [
    ClientsModule.registerAsync([
      {
        name: 'NOTIFICATION_QUEUE',
        useFactory: (configService: ConfigService) => ({
          transport: Transport.RMQ,
          options: {
            urls: [configService.get<string>('RABBITMQ_URI')],
            queue: QueueName.NOTIFICATION_QUEUE,
            queueOptions: {
              durable: true,
            },
          },
        }),
        inject: [ConfigService],
      },
    ]),
    ClientsModule.registerAsync([
      {
        name: 'ASSESSMENT_ENGINE_QUEUE',
        useFactory: (configService: ConfigService) => ({
          transport: Transport.RMQ,
          options: {
            urls: [configService.get<string>('RABBITMQ_URI')],
            queue: QueueName.ASSESSMENT_ENGINE_QUEUE,
            queueOptions: {
              durable: true,
            },
          },
        }),
        inject: [ConfigService],
      },
    ]),
  ],

  providers: [AssessmentEngineQueueService, NotificationQueueService],
  exports: [AssessmentEngineQueueService, NotificationQueueService, ClientsModule],
})
export class QueueModule {}
