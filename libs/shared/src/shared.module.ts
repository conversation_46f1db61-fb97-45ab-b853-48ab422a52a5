import { Modu<PERSON> } from '@nestjs/common';
import { SharedService } from './shared.service';
import { ApLoggerModule } from './logger/logger.module';
import { AuthModule } from './auth/auth.module';
import { CacheModule } from './cache/cache.module';
import { DatabaseModule } from './database/database.module';
import { PasswordEncoder } from './util/password-encoder';
import { ConfigModule } from '@nestjs/config';
import { QueueModule } from './queue/queue.module';

@Module({
  providers: [SharedService, PasswordEncoder],
  imports: [ApLoggerModule, AuthModule, CacheModule, ConfigModule, QueueModule],
  exports: [SharedService, CacheModule, PasswordEncoder, ApLoggerModule, AuthModule],
})
export class SharedModule {}
