import path from 'path';
import { existsSync } from 'fs';

const EMAIL_TEMPLATE_CANDIDATES = [
  process.env.EMAIL_TEMPLATES_DIR,
  path.join(process.cwd(), 'dist', 'apps', 'notification-ms', 'email-templates'),
  path.join(process.cwd(), 'apps', 'notification-ms', 'src', 'email-templates'),
].filter((dir): dir is string => <PERSON><PERSON><PERSON>(dir));

const EMAIL_TEMPLATES_DIR = EMAIL_TEMPLATE_CANDIDATES.find((dir) => existsSync(dir)) ?? EMAIL_TEMPLATE_CANDIDATES.at(-1);

const createTemplate = (subject: string, fileName: string) => ({
  emailSubject: subject,
  messagePath: path.join(EMAIL_TEMPLATES_DIR, fileName),
});

export const EmailTemplate = {
  VIEWS_INCLUDE_PATH: createTemplate('PATH', 'includes'),
  TEST_SCHEDULED: createTemplate('Test Scheduled', 'test-scheduled.html'),
  TEST_RESCHEDULED: createTemplate('Test Rescheduled', 'test-rescheduled.html'),
  TEST_CANCELLED: createTemplate('Test Cancelled', 'test-cancelled.html'),
  FORGET_PASSWORD: createTemplate('Forget Password', 'forget-password.ejs'),
  RESET_PASSWORD: createTemplate('Reset Password', 'reset-password.ejs'),
  WELCOME: createTemplate('Welcome to AptiTest', 'welcome.ejs'),
  WELCOME_BUSINESS: createTemplate('Welcome to AptiTest', 'welcome-business.ejs'),
};

export type EmailTemplateKey = keyof typeof EmailTemplate;
