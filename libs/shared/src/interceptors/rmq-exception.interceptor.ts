import { CallHandler, ExecutionContext, Injectable, Logger, NestInterceptor } from '@nestjs/common';
import { Observable, catchError, tap, throwError } from 'rxjs';
import { RmqContext } from '@nestjs/microservices';

export class RmqAckInterceptor implements NestInterceptor {
  private logger = new Logger(RmqAckInterceptor.name);

  intercept(ctx: ExecutionContext, next: CallHandler): Observable<any> {
    // Only act on RMQ requests; ignore HTTP/WS so you can install it globally.
    const isRmq = ctx.getType<'rpc' | 'http' | 'ws'>() === 'rpc';
    console.log('isRmq', isRmq);
    if (!isRmq) return next.handle();

    const rmqCtx = ctx.switchToRpc().getContext<RmqContext>();
    const channel = rmqCtx.getChannelRef();
    const msg = rmqCtx.getMessage();

    return next.handle().pipe(
      // Ack only after the handler completed successfully
      tap(() => {
        channel.ack(msg);
        this.logger.log('Acknowledged message !!');
      }),
      // On error: choose requeue (true) or send to DLQ/discard (false)
      catchError((err) => {
        console.log('Error occurred, sending to DLQ');
        channel.nack(msg, false, /* requeue */ false);
        return throwError(() => err);
      })
    );
  }
}
