export const EndpointPattern = {
  USER_SERVICE: {
    SIGNUP: 'user.signup',
    SIGNI<PERSON>: 'user.signin',
    GOOGLE_AUTH: 'user.googleAuth',
    SIGNOUT: 'user.signout',
    REFRESH_TOKEN: 'user.refreshToken',
    GET_BUSINESS_INFO: 'user.getBusinessInfo',
    GET_USER_BY_ID: 'user.getUserById',
    CREATE_USER: 'user.createUser',
    UPDATE_USER: 'user.updateUser',
    DELETE_USER: 'user.deleteUser',
    LOCK_USER: 'user.lockUser',
    GET_ACCOUNT_CONFIG: 'user.getAccountConfig',

    ONBOARD_BUSINESS: 'user.onboardBusiness',
  },
  NOTIFICATION_SERVICE: {
    SEND_NOTIFICATION: 'notification.send',
  },

  BLOB_SERVICE: {
    UPLOAD_FILE: 'blob.uploadFile',
    DELETE_FILE: 'blob.deleteFile',
    GET_FILE: 'blob.getFile',
  },

  ASSESSMENT_ENGINE_SERVICE: {
    CREATE_ASSESSMENT: 'assessment.create',
    GET_ASSESSMENT: 'assessment.get',
    UPDATE_ASSESSMENT: 'assessment.update',
    DELETE_ASSESSMENT: 'assessment.delete',
    GET_ASSESSMENT_BY_ID: 'assessment.getById',
    GET_PERFORMANCE_BY_ASSESSMENT_ID: 'assessment.getPerformanceByAssessmentId',
    GET_RESULTS_BY_ASSESSMENT_ID: 'assessment.getResultsByAssessmentId',
    SCHEDULE_ASSESSMENT: 'assessment.schedule',
    SCHEDULE_TEST: 'assessment.scheduleTest',
    VALIDATE_SCHEDULE: 'assessment.validateSchedule',
    GET_SCHEDULES: 'assessment.getSchedules',
    UPDATE_SCHEDULES: 'assessment.updateSchedules',
    DELETE_SCHEDULES: 'assessment.deleteSchedules',
    CREATE_TEST_SESSION: 'assessment.createTestSession',

    CREATE_TEST: 'assessment.createTest', // test create questions
    GET_TEST: 'assessment.getTest',
    UPDATE_TEST: 'assessment.updateTest',
    DELETE_TEST: 'assessment.deleteTest',
    GET_TEST_BY_ID: 'assessment.getTestById',
    STORE_ANSWERS: 'assessment.storeAnswers',
    STORE_PROCTOR_EVENTS: 'assessment.storeProctorEvents',
    END_TEST_SESSION: 'assessment.endTestSession',
    PROCESS_RESULT: 'assessment.processResult',

    GET_PROCTOR_EVENTS: 'assessment.getProctorEvents',

    GET_RESULT: 'assessment.getResult',
    RE_EVALUATE_RESULT: 'assessment.reEvaluateResult',
    GET_RESULT_BY_USER: 'assessment.getResultByUser',
  },
};
