import { EntityManager, Repository } from 'typeorm';
import { BaseRepository } from '../../database/base.repository';
import { TestSession } from '../entities/test-session.entity';
import { InjectRepository } from '@nestjs/typeorm';

export class TestSessionRepository extends BaseRepository<TestSession> {
  constructor(
    protected manager: EntityManager,
    @InjectRepository(TestSession) protected readonly repository: Repository<TestSession>
  ) {
    super(manager, repository);
  }
}
