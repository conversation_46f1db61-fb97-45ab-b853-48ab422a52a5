import { <PERSON>tityManager, Repository, In } from 'typeorm';
import { BaseRepository } from '../../database/base.repository';
import { AptiQuestion } from '../entities/question.entity';
import { InjectRepository } from '@nestjs/typeorm';
import { OptionDto, QuestionDto } from '../dto/request/question.dto';
import { AptiOption } from '../entities/option.entity';

export class QuestionRepository extends BaseRepository<AptiQuestion> {
  constructor(
    protected manager: EntityManager,
    @InjectRepository(AptiQuestion) protected readonly repository: Repository<AptiQuestion>,
    @InjectRepository(AptiOption) protected readonly optionRepository: Repository<AptiOption>
  ) {
    super(manager, repository);
  }

  async findQuestionByTestId(testId: number): Promise<QuestionDto[]> {
    const questions = await this.repository.find({
      where: { test: { id: testId } },
    });

    return await Promise.all(
      questions.map(async (question) => {
        const questionDto = new QuestionDto();
        questionDto.id = question.id;
        questionDto.question = question.questionText;
        questionDto.imageUrl = question.imageUrl;
        questionDto.type = question.questionType;
        const options = await question.options;
        questionDto.options = options?.map((option) => {
          const optionDto = new OptionDto();
          optionDto.id = option.id;
          optionDto.option = option.value;
          optionDto.imageUrl = option.imageUrl;
          return optionDto;
        });
        return questionDto;
      })
    );
  }

  async findByIdsWithOptions(ids: number[]): Promise<AptiQuestion[]> {
    return this.repository.find({
      where: { id: In(ids) },
      relations: ['options'],
    });
  }
}
