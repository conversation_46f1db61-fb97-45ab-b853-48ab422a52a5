import { En<PERSON>tyManager, Repository, In } from 'typeorm';
import { AptiTest } from '../entities/test.entity';
import { BaseRepository } from '../../database/base.repository';
import { SearchDto } from '../../dto/request/search.dto';
import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { TestQuestions } from '../dto/response/test-session-creation.dto';

@Injectable()
export class TestRepository extends BaseRepository<AptiTest> {
  constructor(
    protected manager: EntityManager,
    @InjectRepository(AptiTest) protected readonly repository: Repository<AptiTest>
  ) {
    super(manager, repository);
  }

  getTestForDataTable(searchDto: SearchDto) {
    const qb = this.repository
      .createQueryBuilder('test')
      .select([
        'test.id',
        'test.testName',
        'test.description',
        'test.duration',
        'test.createdBy',
        'test.type',
        'test.createdAt',
        'test.isPaid',
      ])
      .where('test.deleted = :deleted', { deleted: false })
      .andWhere('test.businessId is null');

    if (searchDto.user?.businessId) {
      qb.orWhere('test.businessId = :businessId', {
        businessId: searchDto.user.businessId,
      });
    }

    if (searchDto.filters) {
      for (const key in searchDto.filters) {
        if (!searchDto.filters[key]) {
          continue;
        }
        if (key === 'testName') {
          qb.andWhere(`test.${key} like  :${key}`, { [key]: `%${searchDto.filters[key]}%` });
          continue;
        }

        qb.andWhere(`test.${key} = :${key}`, { [key]: searchDto.filters[key] });
      }
    }
    qb.orderBy('test.createdAt', 'DESC');

    const page = searchDto.page || 1;
    const limit = searchDto.limit || 10;
    qb.offset((page - 1) * limit).limit(limit);

    return qb.getMany();
  }
}
