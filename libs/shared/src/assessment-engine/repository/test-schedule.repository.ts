import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { EntityManager, Repository } from 'typeorm';
import { BaseRepository } from '../../database/base.repository';
import { TestSchedule } from '../entities/test-schedule.entity';

@Injectable()
export class TestScheduleRepository extends BaseRepository<TestSchedule> {
  constructor(
    protected manager: EntityManager,
    @InjectRepository(TestSchedule) protected readonly repository: Repository<TestSchedule>
  ) {
    super(manager, repository);
  }
}
