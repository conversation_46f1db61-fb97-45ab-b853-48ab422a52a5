import { <PERSON><PERSON>ty<PERSON>ana<PERSON>, Repository } from 'typeorm';
import { BaseRepository } from '../../database/base.repository';
import { TestResult } from '../entities/test-result.entity';
import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';

@Injectable()
export class TestResultRepository extends BaseRepository<TestResult> {
  constructor(
    protected manager: EntityManager,
    @InjectRepository(TestResult) protected readonly repository: Repository<TestResult>
  ) {
    super(manager, repository);
  }
}
