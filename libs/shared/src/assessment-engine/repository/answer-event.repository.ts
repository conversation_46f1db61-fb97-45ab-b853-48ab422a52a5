import { Entity<PERSON>anager, Repository } from 'typeorm';
import { BaseRepository } from '../../database/base.repository';
import { AnswerEventEntity } from '../entities/answer-event.entity';
import { AnswerEventDto } from '../dto/request/test-session.dto';
import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';

@Injectable()
export class AnswerEventRepository extends BaseRepository<AnswerEventEntity> {
  constructor(
    protected manager: EntityManager,
    @InjectRepository(AnswerEventEntity) protected readonly repository: Repository<AnswerEventEntity>
  ) {
    super(manager, repository);
  }

  async storeAnswers(answers: AnswerEventDto[]) {
    const answerEvents = answers.map((answer) => {
      const answerEvent = new AnswerEventEntity({});
      answerEvent.sessionId = answer.sessionId;
      answerEvent.questionId = answer.questionId;
      answerEvent.selectedOptionIds = answer.selectedOptionIds;
      answerEvent.stampedAt = answer.ts;
      return answerEvent;
    });

    await this.manager.createQueryBuilder().insert().into(AnswerEventEntity).values(answerEvents).orIgnore().execute();
  }

  async getLatestAnswers(sessionId: string): Promise<AnswerEventEntity[]> {
    const events = await this.repository.find({
      where: { sessionId },
      order: { stampedAt: 'ASC' },
    });

    const latest = new Map<number, AnswerEventEntity>();
    for (const event of events) {
      const existing = latest.get(event.questionId);
      if (!existing || existing.stampedAt < event.stampedAt) {
        latest.set(event.questionId, event);
      }
    }
    return Array.from(latest.values());
  }
}
