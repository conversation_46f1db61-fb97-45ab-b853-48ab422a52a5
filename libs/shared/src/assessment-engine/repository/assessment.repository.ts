import { Entity<PERSON>anager, Repository } from 'typeorm';
import { BaseRepository } from '../../database/base.repository';
import { Assessment } from '../entities/assessment.entity';
import { InjectRepository } from '@nestjs/typeorm';

export class AssessmentRepository extends BaseRepository<Assessment> {
  constructor(
    protected manager: EntityManager,
    @InjectRepository(Assessment) protected readonly repository: Repository<Assessment>
  ) {
    super(manager, repository);
  }

  // Additional methods specific to Assessment can be added here
}
