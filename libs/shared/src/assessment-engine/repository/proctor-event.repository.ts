import { Entity<PERSON>anager, Repository } from 'typeorm';
import { BaseRepository } from '../../database/base.repository';
import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { ProctorEventEntity } from '../entities/proctor-event.entity';
import { createHash } from 'crypto';

@Injectable()
export class ProctorEventRepository extends BaseRepository<ProctorEventEntity> {
  constructor(
    protected manager: EntityManager,
    @InjectRepository(ProctorEventEntity) protected readonly repository: Repository<ProctorEventEntity>
  ) {
    super(manager, repository);
  }

  async store(events: Array<{ sessionId: string; eventType: string; payload?: any; stampedAt: Date }>) {
    const entities = events.map((e) => {
      const entity = new ProctorEventEntity({});
      entity.sessionId = e.sessionId;
      entity.eventType = e.eventType;
      const canonical = e.payload == null ? undefined : stableStringify(e.payload);
      entity.payload = canonical;
      entity.payloadHash = canonical ? sha256(canonical) : undefined;
      entity.stampedAt = e.stampedAt;
      return entity;
    });

    await this.manager.createQueryBuilder().insert().into(ProctorEventEntity).values(entities).orIgnore().execute();
  }

  async findBySession(sessionId: string): Promise<ProctorEventEntity[]> {
    return this.repository.find({ where: { sessionId }, order: { stampedAt: 'ASC' } });
  }
}

function sha256(s: string): string {
  return createHash('sha256').update(s).digest('hex');
}

function stableStringify(value: any): string {
  return JSON.stringify(sortDeep(value));
}

function sortDeep(input: any): any {
  if (Array.isArray(input)) {
    return input.map(sortDeep);
  }
  if (input && typeof input === 'object') {
    const sorted: any = {};
    for (const key of Object.keys(input).sort()) {
      sorted[key] = sortDeep(input[key]);
    }
    return sorted;
  }
  return input;
}
