import { Column, <PERSON>tity, ManyToOne, OneToMany, PrimaryGeneratedColumn } from 'typeorm';
import { BaseEntity } from '../../database/base.entity';
import { AptiOption } from './option.entity';
import { AptiTest } from './test.entity';
import { QuestionType } from '../dto/question-type.enum';

@Entity({ name: 'APTI_QUESTION' })
export class AptiQuestion extends BaseEntity<AptiQuestion> {
  @Column({ nullable: false, name: 'question_text', type: 'text' })
  questionText: string;

  @Column({ nullable: false, name: 'question_type', type: 'varchar', length: 50, enum: QuestionType })
  questionType: QuestionType;

  @OneToMany(() => AptiOption, (option) => option.question, { cascade: true, lazy: true })
  options?: AptiOption[];

  @Column({ nullable: true, name: 'image_url', type: 'varchar', length: 255 })
  imageUrl?: string;

  @ManyToOne(() => AptiTest, (test) => test.questions, { lazy: true })
  test: AptiTest; // Assuming AptiTest is defined elsewhere
}
