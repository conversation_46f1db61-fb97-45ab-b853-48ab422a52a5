import { Column, Entity, PrimaryGeneratedColumn } from 'typeorm';
import { BaseEntity } from '../../database/base.entity';

@Entity({ name: 'APTI_TEST_RESULT' })
export class TestResult extends BaseEntity<TestResult> {
  @Column({ nullable: false, name: 'user_id', type: 'int' })
  userId: number;

  @Column({ nullable: false, name: 'test_id', type: 'int' })
  testId: number;

  @Column({ nullable: false, name: 'score', type: 'int' })
  score: number;

  @Column({ nullable: true, name: 'assessment_id', type: 'int' })
  assessmentId?: number;

  @Column({ nullable: false, name: 'status', type: 'varchar', length: 50 })
  status: string;

  @Column({ nullable: false, name: 'session_id', type: 'varchar', length: 255 })
  sessionId: string;
}
