import { Column, Entity } from 'typeorm';
import { BaseEntity } from '../../database/base.entity';
import { TestScheduleStatus } from '../dto/test-schedule-status.enum';

@Entity({ name: 'APTI_TEST_SCHEDULE' })
export class TestSchedule extends BaseEntity<TestSchedule> {
  @Column({ name: 'test_id', type: 'int', nullable: false })
  testId: number;

  @Column({ name: 'business_id', type: 'int', nullable: false })
  businessId: number;

  @Column({ name: 'start_time', type: 'timestamp', nullable: false })
  startTime: Date;

  @Column({ name: 'end_time', type: 'timestamp', nullable: true })
  endTime?: Date;

  @Column({ name: 'emails', type: 'simple-array', nullable: false })
  emails: string[];

  @Column({ name: 'created_by', type: 'varchar', length: 255, nullable: false })
  createdBy: string;

  @Column({
    name: 'status',
    type: 'enum',
    enum: TestScheduleStatus,
    default: TestScheduleStatus.ACTIVE,
  })
  status: TestScheduleStatus;
}
