import { Column, <PERSON>tity, ManyTo<PERSON>ne, PrimaryGeneratedColumn } from 'typeorm';
import { BaseEntity } from '../../database/base.entity';
import { AptiQuestion } from './question.entity';

@Entity({ name: 'APTI_OPTION' })
export class AptiOption extends BaseEntity<AptiOption> {
  @Column({ nullable: false, name: 'option_text', type: 'text' })
  value: string;

  @Column({ nullable: false, name: 'is_correct', type: 'boolean' })
  isCorrect: boolean;

  @ManyToOne(() => AptiQuestion, (question) => question.options)
  question: AptiQuestion;

  @Column({ nullable: true, name: 'image_url', type: 'varchar', length: 255 })
  imageUrl?: string;
}
