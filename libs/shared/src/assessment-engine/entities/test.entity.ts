import { Column, Entity, OneToMany } from 'typeorm';
import { BaseEntity } from '../../database/base.entity';
import { TestCategory } from '../dto/test-categories.enum';
import { AptiQuestion } from './question.entity';

@Entity({ name: 'APTI_TEST' })
export class AptiTest extends BaseEntity<AptiTest> {
  @Column({ nullable: false, name: 'test_name', type: 'varchar', length: 255 })
  testName: string;

  @Column({ nullable: false, name: 'description', type: 'text' })
  description: string;

  @Column({ nullable: false, name: 'duration', type: 'int' })
  duration: number;

  @Column({ nullable: false, name: 'created_by', type: 'varchar', length: 255 })
  createdBy: string;

  @Column({ nullable: false, name: 'category', type: 'enum', enum: TestCategory })
  type: TestCategory;

  @OneToMany(() => AptiQuestion, (question) => question.test, { lazy: true, cascade: true })
  questions: AptiQuestion[];

  @Column({ nullable: true, name: 'business_id', type: 'int' })
  businessId: number;

  @Column({ nullable: true, name: 'is_paid', type: 'boolean', default: false })
  isPaid: boolean;
}
