import { Column, Entity, Index } from 'typeorm';
import { BaseEntity } from '../../database/base.entity';

@Entity({ name: 'APTI_ANSWER_EVENT' })
@Index('uq_answer_event', ['sessionId', 'questionId', 'stampedAt'], { unique: true })
export class AnswerEventEntity extends BaseEntity<AnswerEventEntity> {
  @Column({ nullable: false, name: 'session_id', type: 'varchar', length: 255 })
  sessionId: string;

  @Column({ nullable: false, name: 'question_id', type: 'int' })
  questionId: number;

  @Column({ nullable: false, name: 'selected_option_ids', type: 'int', array: true })
  selectedOptionIds: number[];

  @Column({ nullable: false, name: 'stamped_at', type: 'timestamp' })
  stampedAt: Date;
}
