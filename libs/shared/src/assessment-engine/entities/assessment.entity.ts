import { <PERSON><PERSON><PERSON>, OneToMany, Column, ManyToMany, JoinTable } from 'typeorm';
import { AptiTest } from './test.entity';
import { BaseEntity } from '../../database/base.entity';

@Entity({ name: 'AP_ASSESSMENT' })
export class Assessment extends BaseEntity<Assessment> {
  @Column({ nullable: false, name: 'name', type: 'varchar', length: 255 })
  name: string;

  @Column({ nullable: false, name: 'enable_proctoring', type: 'boolean' })
  enableProctoring: boolean;

  @Column({ nullable: false, name: 'duration', type: 'int' })
  duration: number;

  @Column({ nullable: false, name: 'created_by', type: 'varchar', length: 255 })
  createdBy: string;

  @ManyToMany(() => AptiTest, { lazy: true })
  @JoinTable({
    name: 'assessment_tests',
    joinColumn: { name: 'assessment_id', referencedColumnName: 'id' },
    inverseJoinColumn: { name: 'test_id', referencedColumnName: 'id' },
  })
  test: AptiTest[];

  @Column({ nullable: true, name: 'business_id', type: 'int' })
  businessId: number;

  @Column({ nullable: true, name: 'is_paid', type: 'boolean', default: false })
  isPaid: boolean;

  @Column({ nullable: true, name: 'description', type: 'text' })
  description: string;
}
