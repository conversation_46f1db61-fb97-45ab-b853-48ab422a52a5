import { Column, Entity, Index } from 'typeorm';
import { BaseEntity } from '../../database/base.entity';

@Entity({ name: 'APTI_PROCTOR_EVENT' })
@Index('uq_proctor_event', ['sessionId', 'eventType', 'stampedAt', 'payloadHash'], { unique: true })
export class ProctorEventEntity extends BaseEntity<ProctorEventEntity> {
  @Column({ nullable: false, name: 'session_id', type: 'varchar', length: 255 })
  sessionId: string;

  @Column({ nullable: false, name: 'event_type', type: 'varchar', length: 100 })
  eventType: string; // e.g., FACE_MATCH, MULTIPLE_FACES, MOTION, TAB_SWITCH, WINDOW_BLUR

  @Column({ nullable: true, name: 'payload', type: 'text' })
  payload?: string; // JSON string with event-specific details

  @Column({ nullable: false, name: 'stamped_at', type: 'timestamp' })
  stampedAt: Date;

  @Column({ nullable: true, name: 'payload_hash', type: 'varchar', length: 64 })
  payloadHash?: string; // SHA-256 of canonical payload JSON
}
