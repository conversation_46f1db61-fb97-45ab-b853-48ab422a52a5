import { Column, Entity, PrimaryGeneratedColumn } from 'typeorm';
import { BaseEntity } from '../../database/base.entity';
import { TestSessionStatus } from '../dto/test-session-status.enum';

@Entity({ name: 'APTI_TEST_SESSION' })
export class TestSession extends BaseEntity<TestSession> {
  @Column({ nullable: false, name: 'user_id', type: 'int' })
  userId: number;

  @Column({ nullable: false, name: 'session_id', type: 'varchar', length: 255 })
  sessionId: string;

  @Column({ nullable: false, name: 'test_id', type: 'int' })
  testId: number;

  @Column({ nullable: false, name: 'status', type: 'varchar', length: 50, enum: TestSessionStatus })
  status: TestSessionStatus;

  @Column({ nullable: true, name: 'start_time', type: 'timestamp' })
  startTime?: Date;

  @Column({ nullable: true, name: 'end_time', type: 'timestamp' })
  endTime?: Date;

  @Column({ nullable: true, name: 'assessment_id', type: 'int' })
  assessmentId?: number;
}
