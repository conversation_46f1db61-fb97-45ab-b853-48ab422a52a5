import { ApiProperty } from '@nestjs/swagger';
import { QuestionType } from '../question-type.enum';
import { IsEnum, IsNotEmpty, IsNumber, IsArray, ValidateNested } from 'class-validator';
import { TestCategory } from '../test-categories.enum';
import { ICustomRequest } from '../../../dto/custom-request.dts';
import { Type } from 'class-transformer';

export class CreateTestDto {
  @ApiProperty({ description: 'The name of the test' })
  @IsNotEmpty()
  testName: string;

  @ApiProperty({ description: 'Category of the test' })
  @IsNotEmpty()
  @IsEnum(TestCategory)
  type: TestCategory;

  @ApiProperty({ description: 'The description of the test' })
  @IsNotEmpty()
  description: string;

  @ApiProperty({ description: 'The duration of the test in seconds' })
  @IsNumber()
  duration: number;

  @ApiProperty({ description: 'Array of questions for the test' })
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => QuestionDto)
  questions: QuestionDto[];

  author: ICustomRequest;
}

export class UpdateTestDto extends CreateTestDto {
  @IsNumber()
  @ApiProperty({ description: 'The ID of the test to update' })
  id: number; // ID of the test to update
}

export class QuestionDto {
  id: number;

  @ApiProperty({ description: 'The question text' })
  @IsNotEmpty()
  question: string;

  @ApiProperty({ description: 'Optional image URL for the question', required: false })
  imageUrl?: string;

  @ApiProperty({ description: 'The category of the question' })
  @IsNotEmpty()
  category: string;

  @IsNotEmpty()
  @ApiProperty({ description: 'The type of the question' }) // e.g., 'REASONING', 'VERBAL_REASONING', etc.
  type: QuestionType; // e.g., 'MCQ', 'FILL_IN_THE_BLANK', etc.// Optional image URL for the question

  @IsNotEmpty()
  @ApiProperty({ description: 'The options for the question' })
  options: OptionDto[]; // Array of options for the question
}

export class OptionDto {
  id: number;

  @ApiProperty({ description: 'The option text' })
  @IsNotEmpty()
  option: string;
  @ApiProperty({ description: 'Indicates if the option is correct' })
  isCorrect: boolean;

  @ApiProperty({ description: 'Optional image URL for the option', required: false })
  imageUrl?: string;
}

export class UpdateQuestionDto extends QuestionDto {
  @IsNumber()
  @ApiProperty({ description: 'The ID of the question to update' })
  id: number; // ID of the question to update

  @ApiProperty({ description: 'Indicates if the question is deleted', required: false })
  deleted?: boolean;
}

export class UpdateOptionDto extends OptionDto {
  @IsNumber()
  @ApiProperty({ description: 'The ID of the option to update' })
  id: number; // ID of the option to update

  @ApiProperty({ description: 'Indicates if the option is deleted', required: false })
  @IsNotEmpty()
  deleted?: boolean;
}
