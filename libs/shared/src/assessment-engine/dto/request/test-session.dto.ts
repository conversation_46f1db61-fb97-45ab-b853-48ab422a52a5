import { ApiProperty } from '@nestjs/swagger';
import { IsNumber, IsOptional } from 'class-validator';
import { ICustomRequest } from '../../../dto/custom-request.dts';

export class CreateTestSessionDto {
  @ApiProperty({ description: 'The ID of the test' })
  @IsOptional()
  @IsNumber()
  testId: number;

  @ApiProperty({ description: 'The ID of the assessment' })
  @IsOptional()
  @IsNumber()
  assessmentId: number;

  author: ICustomRequest;
}

export class AnswerEventDto {
  @ApiProperty({ description: 'The session ID' })
  sessionId: string;

  @ApiProperty({ description: 'The question ID' })
  questionId: number;

  @ApiProperty({ description: 'The selected option IDs' })
  selectedOptionIds: number[];

  @ApiProperty({ description: 'The timestamp of the event' })
  ts: Date;
}
