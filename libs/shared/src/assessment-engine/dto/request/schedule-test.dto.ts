import { ApiProperty } from '@nestjs/swagger';
import { ICustomRequest } from '../../../dto/custom-request.dts';

export class ScheduleTestDto {
  @ApiProperty({ description: 'Test id to schedule' })
  testId: number;

  @ApiProperty({ description: 'Start time in ISO format' })
  startTime: string;

  @ApiProperty({ description: 'End time in ISO format', required: false })
  endTime?: string;

  @ApiProperty({ description: 'Participant email' })
  email: string;

  author: ICustomRequest;
}
