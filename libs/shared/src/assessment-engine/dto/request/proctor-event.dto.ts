import { ApiProperty } from '@nestjs/swagger';

export class ProctorEventDto {
  @ApiProperty({ description: 'The session ID' })
  sessionId: string;

  @ApiProperty({ description: 'Event type e.g., FACE_MATCH, MULTIPLE_FACES, MOTION, TAB_SWITCH, WINDOW_BLUR' })
  eventType: string;

  @ApiProperty({ description: 'Event timestamp' })
  ts: Date;

  @ApiProperty({ description: 'Optional event payload (JSON object)', required: false })
  payload?: Record<string, any>;
}
