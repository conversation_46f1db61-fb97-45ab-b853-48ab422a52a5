import { ApiProperty } from '@nestjs/swagger';
import { ICustomRequest } from '../../../dto/custom-request.dts';
export class CreateAssessmentDto {
  @ApiProperty({ description: 'The ID of the assessment' })
  name: string;

  @ApiProperty({ description: 'Enable proctoring for the assessment' })
  enableProctoring: boolean;

  @ApiProperty({ description: 'The duration of the assessment in seconds' })
  duration: number;

  @ApiProperty({ description: 'The combination of tests to be included in the assessment' })
  testIds: number[];

  author: ICustomRequest;
}

export class UpdateAssessmentDto extends CreateAssessmentDto {
  @ApiProperty({ description: 'The ID of the assessment to update' })
  id: number;
}
