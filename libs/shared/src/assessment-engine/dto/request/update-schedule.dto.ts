import { ApiProperty } from '@nestjs/swagger';
import { ICustomRequest } from '../../../dto/custom-request.dts';

export enum ScheduleAction {
  RESCHEDULE = 'RESCHEDULE',
  CANCEL = 'CANCEL',
}

export class UpdateScheduleDto {
  @ApiProperty({ type: [Number], description: 'Schedule ids to update' })
  ids: number[];

  @ApiProperty({ enum: ScheduleAction, description: 'Action to perform' })
  action: ScheduleAction;

  @ApiProperty({ description: 'New start time in ISO format', required: false })
  startTime?: string;

  @ApiProperty({ description: 'New end time in ISO format', required: false })
  endTime?: string;

  author: ICustomRequest;
}
