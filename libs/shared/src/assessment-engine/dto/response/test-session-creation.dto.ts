import { AptiQuestion } from '../../entities/question.entity';
import { QuestionDto } from '../request/question.dto';
import { TestSessionStatus } from '../test-session-status.enum';

export class TestSessionCreationResponseDto {
  id: number;
  duration: number;
  status: TestSessionStatus;
  sessionId: string;
  assessment: {
    id: number;
    name: string;
    description: string;
    duration: number;
  };
  test: TestQuestions[];
  instructions: {
    general: string;
    perQuestion: string;
    timeLimit: string;
    nativeLanguage: string;
  };
}

export class TestQuestions {
  id: number;
  questions: QuestionDto[];
  duration: number;
}
