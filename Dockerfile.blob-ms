FROM node:20-alpine as builder

WORKDIR /usr/src/app

# Copy package.json and other configuration files
COPY package*.json ./
COPY apps/blob-ms/package.json apps/blob-ms/
COPY tsconfig*.json ./
COPY nest-cli.json ./

# Ensure the apps directory exists
RUN mkdir -p apps

# Copy the api-gateway application and shared libraries
COPY apps/blob-ms apps/blob-ms
COPY libs libs

# Install dependencies and build the application
RUN npm install
RUN npm run build blob-ms

# Production stage
FROM node:20-alpine

ARG NODE_ENV=production
ENV NODE_ENV=${NODE_ENV}
ENV TZ=UTC

WORKDIR /usr/src/app

# Install logrotate (Alpine Linux uses apk)
RUN apk add --no-cache logrotate tzdata

# Create log directory (mapped to host via docker-compose)
RUN mkdir -p /usr/src/app/logs

# Copy logrotate configuration
COPY ./logrotate.conf /etc/logrotate.d/applogs

# Set logrotate to run daily (Alpine uses crond)
RUN echo "0 0 * * * /usr/sbin/logrotate -f /etc/logrotate.d/applogs" >> /etc/crontabs/root

# Start cron in the background (Alpine)
RUN crond

# Copy package.json and install production dependencies
COPY package*.json ./
COPY apps/blob-ms/package.json apps/blob-ms/
RUN npm install --production

# Copy the built application from the builder stage
COPY --from=builder /usr/src/app/dist ./dist

EXPOSE 3000
CMD ["node", "dist/apps/blob-ms/main"]