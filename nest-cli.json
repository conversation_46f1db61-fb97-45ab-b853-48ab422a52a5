{"$schema": "https://json.schemastore.org/nest-cli", "collection": "@nestjs/schematics", "sourceRoot": "apps/api-gateway/src", "compilerOptions": {"deleteOutDir": true, "webpack": true, "tsConfigPath": "apps/api-gateway/tsconfig.app.json"}, "monorepo": true, "root": "apps/api-gateway", "projects": {"api-gateway": {"type": "application", "root": "apps/api-gateway", "entryFile": "main", "sourceRoot": "apps/api-gateway/src", "compilerOptions": {"tsConfigPath": "apps/api-gateway/tsconfig.app.json"}}, "assessment-engine-ms": {"type": "application", "root": "apps/assessment-engine-ms", "entryFile": "main", "sourceRoot": "apps/assessment-engine-ms/src", "compilerOptions": {"tsConfigPath": "apps/assessment-engine-ms/tsconfig.app.json"}}, "blob-ms": {"type": "application", "root": "apps/blob-ms", "entryFile": "main", "sourceRoot": "apps/blob-ms/src", "compilerOptions": {"tsConfigPath": "apps/blob-ms/tsconfig.app.json"}}, "notification-ms": {"type": "application", "root": "apps/notification-ms", "entryFile": "main", "sourceRoot": "apps/notification-ms/src", "compilerOptions": {"tsConfigPath": "apps/notification-ms/tsconfig.app.json"}}, "shared": {"type": "library", "root": "libs/shared", "entryFile": "index", "sourceRoot": "libs/shared/src", "compilerOptions": {"tsConfigPath": "libs/shared/tsconfig.lib.json"}}, "user-service": {"type": "application", "root": "apps/user-service", "entryFile": "main", "sourceRoot": "apps/user-service/src", "compilerOptions": {"tsConfigPath": "apps/user-service/tsconfig.app.json"}}}}