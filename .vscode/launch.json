{"version": "0.2.0", "configurations": [{"name": "Debug API Gateway", "type": "node", "request": "attach", "port": 9210, "address": "localhost", "localRoot": "${workspaceFolder}", "remoteRoot": "/usr/src/app"}, {"name": "Debug User Service", "type": "node", "request": "attach", "port": 9229, "address": "localhost", "localRoot": "${workspaceFolder}", "remoteRoot": "/usr/src/app"}, {"name": "Debug Blob MS", "type": "node", "request": "attach", "port": 9230, "address": "localhost", "localRoot": "${workspaceFolder}", "remoteRoot": "/usr/src/app"}, {"name": "Debug Assessment Engine MS", "type": "node", "request": "attach", "port": 9231, "address": "localhost", "localRoot": "${workspaceFolder}", "remoteRoot": "/usr/src/app"}]}