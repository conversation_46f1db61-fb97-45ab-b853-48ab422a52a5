import { Module } from '@nestjs/common';
import { ApiGatewayController } from './api-gateway.controller';
import { UsersModule } from './users/users.module';
import { ConfigModule } from '@nestjs/config';
import { LoggerModule } from 'nestjs-pino';
import { SharedModule } from '@app/shared/shared.module';
import { APP_GUARD } from '@nestjs/core';
import { AuthGuard } from '../../../libs/shared/src/auth/guard/auth.guard';
import { BlobMsModule } from './blob-ms/blob-ms.module';
import { AssessmentEngineModule } from './assessment-engine/assessment-engine.module';

@Module({
  imports: [
    UsersModule,
    ConfigModule.forRoot({
      isGlobal: true,
      envFilePath: './.env',
    }),
    LoggerModule.forRoot({
      pinoHttp: {
        name: 'api-gateway',
      },
    }),
    SharedModule,
    BlobMsModule,
    AssessmentEngineModule,
  ],
  controllers: [ApiGatewayController],
  providers: [
    {
      provide: APP_GUARD,
      useClass: AuthGuard,
    },
  ],
})
export class ApiGatewayModule {}
