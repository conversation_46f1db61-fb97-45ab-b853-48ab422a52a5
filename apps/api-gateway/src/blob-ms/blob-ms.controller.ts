import { <PERSON>, Param, Post, UploadedFile } from '@nestjs/common';
import { UseInterceptors } from '@nestjs/common';
import { FileInterceptor } from '@nestjs/platform-express';
import { ApiTags } from '@nestjs/swagger';
import { Express } from 'express';
import { BlobMsService } from './blob-ms.service';
import { ResponseDto } from '../../../../libs/shared/src/dto/resp/base-response.dto';
import { CustomException } from '../../../../libs/shared/src/exception/custom.exception';

@ApiTags('Blob Management Service')
@Controller('blob-ms')
@UseInterceptors(FileInterceptor('file'))
@Controller()
export class BlobMsController {
  constructor(private readonly blobService: BlobMsService) {}

  @Post('upload')
  @UseInterceptors(
    FileInterceptor('file', {
      limits: { fileSize: 5 * 1024 * 1024 }, // 5MB limit
    })
  )
  async uploadFile(@UploadedFile() file: Express.Multer.File) {
    if (!file) {
      throw new CustomException('No file uploaded or file size exceeds limit of 5MB');
    }
    await this.blobService.uploadFile(file);
  }

  @Post('download/:fileId')
  async downloadFile(@Param('fileId') fileId: string) {
    // Logic to download the file from blob storage using the fileId
    return await this.blobService.downloadFile(fileId);
  }
}
