import { Modu<PERSON> } from '@nestjs/common';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { BlobMsService } from './blob-ms.service';
import { ClientProxyFactory, Transport } from '@nestjs/microservices';
import { BlobMsController } from './blob-ms.controller';

@Module({
  imports: [
    ConfigModule.forRoot({
      isGlobal: true,
      envFilePath: './.env',
    }),
  ],
  controllers: [BlobMsController],

  providers: [
    BlobMsService,
    {
      provide: 'BLOB_MS_SERVICE',
      useFactory: (configService: ConfigService) => {
        return ClientProxyFactory.create({
          transport: Transport.TCP,
          options: {
            host: configService.get('BLOB_MS_HOST'),
            port: configService.get('BLOB_MS_PORT'),
          },
        });
      },
      inject: [ConfigService],
    },
  ],
})
export class BlobMsModule {}
