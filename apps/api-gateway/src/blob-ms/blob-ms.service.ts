import { Inject, Injectable } from '@nestjs/common';
import { ResponseDto } from '../../../../libs/shared/src/dto/resp/base-response.dto';
import { ClientProxy } from '@nestjs/microservices';
import { EndpointPattern } from '../../../../libs/shared/src/util/endpoint-pattern';

@Injectable()
export class BlobMsService {
  // This service will handle blob storage operations
  // For example, uploading, downloading, and deleting files
  constructor(@Inject('BLOB_MS_SERVICE') private readonly blobServiceClient: ClientProxy) {
    // Initialize any necessary dependencies or configurations
  }

  async uploadFile(file: Express.Multer.File) {
    return this.blobServiceClient.send(EndpointPattern.BLOB_SERVICE.UPLOAD_FILE, file);
  }

  async downloadFile(fileId: string) {
    return this.blobServiceClient.send(EndpointPattern.BLOB_SERVICE.GET_FILE, { fileId });
  }

  async deleteFile(fileId: string) {
    return this.blobServiceClient.send(EndpointPattern.BLOB_SERVICE.DELETE_FILE, { fileId });
  }
}
