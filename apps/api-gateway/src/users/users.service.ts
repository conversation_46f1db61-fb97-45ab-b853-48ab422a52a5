import { Inject, Injectable } from '@nestjs/common';
import { ClientProxy } from '@nestjs/microservices';
import { UserSignupDto } from '@app/shared/dto/user-signup.dto';
import { EndpointPattern } from '@app/shared/util/endpoint-pattern';
import { UserSigninDto } from '../../../../libs/shared/src/dto/user-signin.dto';
import { GoogleAuthDto } from '@app/shared/dto/google-auth.dto';
import { BusinessAccountCreation } from '../../../../libs/shared/src/dto/request/business-account.dto';

@Injectable()
export class UsersService {
  constructor(@Inject('USER_SERVICE') private readonly userService: ClientProxy) {}

  async getUsers() {
    return await this.userService.send('user.findAll', {});
  }

  signup(payload: UserSignupDto) {
    return this.userService.send(EndpointPattern.USER_SERVICE.SIGNUP, payload);
  }
  signin(payload: UserSigninDto) {
    return this.userService.send(EndpointPattern.USER_SERVICE.SIGNIN, payload);
  }
  googleAuth(payload: GoogleAuthDto) {
    return this.userService.send(EndpointPattern.USER_SERVICE.GOOGLE_AUTH, payload);
  }
  logout(token: string) {
    return this.userService.send(EndpointPattern.USER_SERVICE.SIGNOUT, token);
  }
  refreshToken(token: string) {
    return this.userService.send(EndpointPattern.USER_SERVICE.REFRESH_TOKEN, { token });
  }

  onboardBusiness(payload: BusinessAccountCreation) {
    return this.userService.send(EndpointPattern.USER_SERVICE.ONBOARD_BUSINESS, payload);
  }
}
