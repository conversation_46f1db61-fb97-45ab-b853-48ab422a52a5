import { Body, Controller, Post } from '@nestjs/common';
import { UsersService } from './users.service';
import { ApiOperation } from '@nestjs/swagger';
import { BusinessAccountCreation } from '@app/shared/dto/request/business-account.dto';
import { ApiTags } from '@nestjs/swagger';
import { Public } from '@app/shared/decorators/public.decorator';

@ApiTags('Business Account Controller')
@Controller('business-account')
export class BusinessAccountController {
  constructor(private readonly userService: UsersService) {}

  @Public()
  @ApiOperation({ summary: 'Onboard a new business' })
  @Post('onboard')
  async onboardBusiness(@Body() payload: BusinessAccountCreation) {
    return await this.userService.onboardBusiness(payload);
  }
}
