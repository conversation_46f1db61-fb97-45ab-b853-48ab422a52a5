import { Body, Controller, Get, Post, Req } from '@nestjs/common';
import { UsersService } from './users.service';
import { ApiBearerAuth, ApiOperation } from '@nestjs/swagger';
import { UserSignupDto } from '@app/shared/dto/user-signup.dto';
import { UserSigninDto } from '@app/shared/dto/user-signin.dto';
import { GoogleAuthDto } from '@app/shared/dto/google-auth.dto';
import { Public } from '../../../../libs/shared/src/decorators/public.decorator';
import { Request } from 'express';

@ApiBearerAuth()
@Controller('users')
export class UsersController {
  constructor(private readonly userService: UsersService) {}

  @Get()
  getUsers() {
    return this.userService.getUsers();
  }

  @Public()
  @ApiOperation({ summary: 'Signup a new user' })
  @Post('signup')
  async signup(@Body() payload: UserSignupDto) {
    return await this.userService.signup(payload);
  }

  @Public()
  @ApiOperation({ summary: 'Signin a user' })
  @Post('signin')
  async signin(@Body() payload: UserSigninDto) {
    return await this.userService.signin(payload);
  }

  @Public()
  @ApiOperation({ summary: 'Signin or signup with Google' })
  @Post('google')
  async googleAuth(@Body() payload: GoogleAuthDto) {
    return await this.userService.googleAuth(payload);
  }

  @ApiOperation({ summary: 'Logout a user' })
  @Post('logout')
  async logout(@Req() req: Request) {
    const token = req.headers.authorization?.split(' ')[1];
    return await this.userService.logout(token);
  }

  @ApiOperation({ summary: 'Refresh Token Endpoint' })
  @Post('refresh-token')
  async refreshToken(@Body('token') token: string) {
    return await this.userService.refreshToken(token);
  }
}
