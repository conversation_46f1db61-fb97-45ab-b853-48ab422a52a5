import { Body, Controller, Delete, Get, Param, Post, Put, Query } from '@nestjs/common';
import { ApiBearerAuth, ApiOperation, ApiTags } from '@nestjs/swagger';
import { AssessmentEngineService } from '../services/assessment-engine.service';
import { CreateAssessmentDto, UpdateAssessmentDto } from '@app/shared/assessment-engine/dto/request/assessment.dto';
import { ICustomRequest } from '@app/shared/dto/custom-request.dts';
import { UserReq } from '@app/shared/decorators/user.decorator';

@ApiTags('Assessment Engine')
@ApiBearerAuth()
@Controller('assessment')
export class AssessmentEngineController {
  constructor(private readonly assessmentEngineService: AssessmentEngineService) {}

  @ApiOperation({ summary: 'Create Assessment' })
  @Post()
  createAssessment(@Body() assessmentData: CreateAssessmentDto, @UserReq() user: ICustomRequest) {
    assessmentData.author = user;
    return this.assessmentEngineService.createAssessment(assessmentData);
  }

  @ApiOperation({ summary: 'Get Assessment by Name and Date' })
  @Get()
  getAssessment(@Query() query: { name?: string; date: string }) {
    return this.assessmentEngineService.getAssessment(query.name, query.date);
  }

  @ApiOperation({ summary: ' update Assessment' })
  @Put()
  updateAssessment(@Body() assessmentData: UpdateAssessmentDto) {
    return this.assessmentEngineService.updateAssessment(assessmentData);
  }

  @ApiOperation({ summary: 'Delete Assessment' })
  @Delete(':id')
  deleteAssessment(@Param('id') id: number) {
    return this.assessmentEngineService.deleteAssessment(id);
  }

  @ApiOperation({ summary: 'Start Assessment' })
  @Post('start/:id')
  startAssessment(@Param('id') id: number) {
    return this.assessmentEngineService.startAssessment(id);
  }
}
