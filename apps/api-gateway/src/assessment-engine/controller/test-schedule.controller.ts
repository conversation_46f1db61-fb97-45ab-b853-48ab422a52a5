import { Body, Controller, Param, Post, Get, Patch, Delete } from '@nestjs/common';
import { ApiBearerAuth, ApiOperation, ApiTags } from '@nestjs/swagger';
import { UserReq } from '@app/shared/decorators/user.decorator';
import { ICustomRequest } from '@app/shared/dto/custom-request.dts';
import { ScheduleTestDto } from '@app/shared/assessment-engine/dto/request/schedule-test.dto';
import { TestService } from '../services/test.service';
import { firstValueFrom } from 'rxjs';
import { JwtService } from '@nestjs/jwt';
import { ConfigService } from '@nestjs/config';
import { UpdateScheduleDto } from '@app/shared/assessment-engine/dto/request/update-schedule.dto';
import { DeleteScheduleDto } from '@app/shared/assessment-engine/dto/request/delete-schedule.dto';

@ApiTags('Test Schedule')
@ApiBearerAuth()
@Controller('assessment/schedule')
export class TestScheduleController {
  constructor(
    private readonly testService: TestService,
    private readonly jwtService: JwtService,
    private readonly configService: ConfigService
  ) {}

  @ApiOperation({ summary: 'Schedule a test' })
  @Post()
  scheduleTest(@Body() dto: ScheduleTestDto, @UserReq() user: ICustomRequest) {
    dto.author = user;
    return this.testService.scheduleTest(dto);
  }

  @ApiOperation({ summary: 'Get all schedules' })
  @Get()
  getSchedules(@UserReq() user: ICustomRequest) {
    return this.testService.getSchedules(user);
  }

  @ApiOperation({ summary: 'Update schedules (reschedule or cancel)' })
  @Patch()
  updateSchedules(@Body() dto: UpdateScheduleDto, @UserReq() user: ICustomRequest) {
    dto.author = user;
    return this.testService.updateSchedules(dto);
  }

  @ApiOperation({ summary: 'Delete schedules' })
  @Delete()
  deleteSchedules(@Body() dto: DeleteScheduleDto, @UserReq() user: ICustomRequest) {
    dto.author = user;
    return this.testService.deleteSchedules(dto);
  }

  @ApiOperation({ summary: 'Validate participant and issue token' })
  @Post(':id/validate')
  async validate(@Param('id') id: number, @Body() body: { email: string; firstName: string }) {
    const resp = await firstValueFrom(this.testService.validateSchedule(id, body.email));
    const { testId, duration } = resp.data;
    const token = this.jwtService.sign(
      { email: body.email, firstName: body.firstName, testId },
      {
        secret: this.configService.get<string>('JWT_SECRET'),
        expiresIn: Math.ceil(duration * 1.3),
      }
    );
    return { token };
  }
}
