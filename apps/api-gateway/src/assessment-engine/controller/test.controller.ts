import { Body, Controller, Get, Post, Put, Query, Param, Delete } from '@nestjs/common';
import { ApiBearerAuth, ApiOperation, ApiQuery, ApiTags } from '@nestjs/swagger';
import { CreateTestDto, UpdateTestDto } from '@app/shared/assessment-engine/dto/request/question.dto';
import { TestService } from '../services/test.service';
import { SearchDto } from '@app/shared/dto/request/search.dto';
import { UserReq } from '@app/shared/decorators/user.decorator';
import { ICustomRequest } from '@app/shared/dto/custom-request.dts';
import { TestCategory } from '../../../../../libs/shared/src/assessment-engine/dto/test-categories.enum';

@ApiTags('Test Controller')
@ApiBearerAuth()
@Controller('assessment/test')
export class TestController {
  constructor(private readonly testService: TestService) {}

  @ApiOperation({ summary: 'Create Test' })
  @Post()
  createAssessment(@Body() test: CreateTestDto, @UserReq() user: ICustomRequest) {
    test.author = user;
    return this.testService.createTest(test);
  }

  @ApiOperation({ summary: 'Get all Tests by name and Date' })
  @ApiQuery({ name: 'page', required: false, type: Number })
  @ApiQuery({ name: 'limit', required: false, type: Number })
  @ApiQuery({ name: 'testName', required: false, type: String })
  @ApiQuery({ name: 'type', required: false, enum: TestCategory })
  @Get()
  getAssessment(@Query() query: Record<string, any>) {
    const searchDto = new SearchDto();
    searchDto.filters = { ...query, page: null, limit: null };
    searchDto.page = query.page;
    searchDto.limit = query.limit;

    return this.testService.getTest(searchDto);
  }

  @ApiOperation({ summary: 'Get Test by ID' })
  @Get(':id')
  getAssessmentById(@Param('id') id: number) {
    return this.testService.getTestById(id);
  }

  @ApiOperation({ summary: 'Update Test' })
  @Put()
  updateAssessment(@Body() assessmentData: UpdateTestDto) {
    return this.testService.updateTest(assessmentData);
  }

  @ApiOperation({ summary: 'Delete Test' })
  @Delete()
  deleteAssessment(@Body('id') id: number) {
    return this.testService.deleteTest(id);
  }

  @ApiOperation({ summary: 'Get Result by Session ID' })
  @Get('result/:sessionId')
  getResultById(@Param('sessionId') sessionId: string, @UserReq() user: ICustomRequest) {
    return this.testService.getResultById(sessionId, user);
  }

  @ApiOperation({ summary: 'Re-evaluate Result by  Test ID ' })
  @Get('re-evaluate/:id')
  reEvaluateResult(@Param('id') id: number, @UserReq() user: ICustomRequest) {
    return this.testService.reEvaluateResult(id, user.businessId);
  }

  @ApiOperation({ summary: 'Get Result by User ID ' })
  @Get(':testId/result/user/:userId')
  getResultByUser(@Param('userId') userId: number, @Param('testId') testId: number) {
    return this.testService.getResultByUser(userId, testId);
  }
}
