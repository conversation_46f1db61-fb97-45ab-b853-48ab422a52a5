import { Body, Controller, Get, Post, Put, Query, Param, Delete } from '@nestjs/common';

import { ApiBearerAuth, ApiOperation, ApiQuery, ApiTags, ApiBody } from '@nestjs/swagger';
import { SearchDto } from '@app/shared/dto/request/search.dto';
import { UserReq } from '@app/shared/decorators/user.decorator';
import { ICustomRequest } from '@app/shared/dto/custom-request.dts';
import { AnswerEventDto, CreateTestSessionDto } from '@app/shared/assessment-engine/dto/request/test-session.dto';
import { ProctorEventDto } from '@app/shared/assessment-engine/dto/request/proctor-event.dto';
import { TestService } from '../services/test.service';

@ApiTags('Test Session Controller')
@ApiBearerAuth()
@Controller('assessment/test-session')
export class TestSessionController {
  constructor(private readonly testSessionService: TestService) {}

  @ApiOperation({ summary: 'Create Test Session' })
  @Post()
  createTestSession(@Body() testSession: CreateTestSessionDto, @UserReq() user: ICustomRequest) {
    testSession.author = user;
    return this.testSessionService.createTestSession(testSession);
  }

  @ApiOperation({ summary: 'Store answers for a test session' })
  @ApiBody({ type: [AnswerEventDto], required: true, description: 'Array of answer events' })
  @Put(':sessionId/answers')
  storeAnswers(@Param('sessionId') sessionId: string, @Body() answers: AnswerEventDto[]) {
    return this.testSessionService.storeAnswers(sessionId, answers);
  }

  @ApiOperation({ summary: 'End test session' })
  @Put(':sessionId/end')
  endTestSession(@Param('sessionId') sessionId: string) {
    return this.testSessionService.endTestSession(sessionId);
  }

  @ApiOperation({ summary: 'Store proctoring/activity events for a test session' })
  @ApiBody({ type: [ProctorEventDto], required: true, description: 'Array of proctoring/activity events' })
  @Put(':sessionId/proctoring-events')
  storeProctorEvents(@Param('sessionId') sessionId: string, @Body() events: ProctorEventDto[]) {
    return this.testSessionService.storeProctorEvents(sessionId, events);
  }

  @ApiOperation({ summary: 'Get proctoring/activity events for a test session' })
  @Get(':sessionId/proctoring-events')
  getProctorEvents(@Param('sessionId') sessionId: string) {
    return this.testSessionService.getProctorEvents(sessionId);
  }
}
