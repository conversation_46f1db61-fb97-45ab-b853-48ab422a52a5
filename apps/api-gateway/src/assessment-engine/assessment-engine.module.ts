import { Module } from '@nestjs/common';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { ClientProxyFactory, Transport } from '@nestjs/microservices';
import { AssessmentEngineController } from './controller/assessment.controller';
import { AssessmentEngineService } from './services/assessment-engine.service';
import { TestService } from './services/test.service';
import { TestController } from './controller/test.controller';
import { TestSessionController } from './controller/test-session.controller';
import { QueueModule } from '@app/shared/queue/queue.module';
import { TestScheduleController } from './controller/test-schedule.controller';
import { SharedModule } from '@app/shared/shared.module';

@Module({
  imports: [
    ConfigModule.forRoot({
      isGlobal: true,
      envFilePath: './.env',
    }),
    QueueModule,
    SharedModule,
  ],
  controllers: [AssessmentEngineController, TestController, TestSessionController, TestScheduleController],
  providers: [
    {
      provide: 'ASSESSMENT_ENGINE_MS',
      useFactory: (configService: ConfigService) => {
        return ClientProxyFactory.create({
          transport: Transport.TCP,
          options: {
            host: configService.get('ASSESSMENT_ENGINE_MS_HOST'),
            port: configService.get('ASSESSMENT_ENGINE_MS_PORT'),
          },
        });
      },
      inject: [ConfigService],
    },
    AssessmentEngineService,
    TestService, // Ensure this service is defined in the same file or imported correctly
  ],
})
export class AssessmentEngineModule {}
