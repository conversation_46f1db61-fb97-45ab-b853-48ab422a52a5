import { Injectable } from '@nestjs/common';
import { Inject } from '@nestjs/common';
import { ClientProxy } from '@nestjs/microservices';
import { CreateAssessmentDto, UpdateAssessmentDto } from '@app/shared/assessment-engine/dto/request/assessment.dto';
import { EndpointPattern } from '@app/shared/util/endpoint-pattern';

@Injectable()
export class AssessmentEngineService {
  constructor(@Inject('ASSESSMENT_ENGINE_MS') private readonly assessmentEngineClient: ClientProxy) {}

  deleteAssessment(id: number) {
    return this.assessmentEngineClient.send(EndpointPattern.ASSESSMENT_ENGINE_SERVICE.DELETE_ASSESSMENT, { id });
  }
  updateAssessment(assessmentData: UpdateAssessmentDto) {
    return this.assessmentEngineClient.send(
      EndpointPattern.ASSESSMENT_ENGINE_SERVICE.UPDATE_ASSESSMENT,
      assessmentData
    );
  }
  getAssessment(name: string, date: string) {
    return this.assessmentEngineClient.send(EndpointPattern.ASSESSMENT_ENGINE_SERVICE.GET_ASSESSMENT, { name, date });
  }

  async createAssessment(assessmentData: CreateAssessmentDto) {
    return await this.assessmentEngineClient.send(
      EndpointPattern.ASSESSMENT_ENGINE_SERVICE.CREATE_ASSESSMENT,
      assessmentData
    );
  }
  startAssessment(id: number) {
    return this.assessmentEngineClient.send(EndpointPattern.ASSESSMENT_ENGINE_SERVICE.CREATE_TEST_SESSION, {
      assessmentId: id,
    });
  }
}
