import { Inject, Injectable } from '@nestjs/common';

import { CreateTestDto, UpdateTestDto } from '@app/shared/assessment-engine/dto/request/question.dto';
import { ClientProxy } from '@nestjs/microservices';
import { EndpointPattern } from '@app/shared/util/endpoint-pattern';
import { SearchDto } from '@app/shared/dto/request/search.dto'; // Adjust the import path as necessary
import { AnswerEventDto, CreateTestSessionDto } from '@app/shared/assessment-engine/dto/request/test-session.dto';
import { ProctorEventDto } from '@app/shared/assessment-engine/dto/request/proctor-event.dto';
import { AssessmentEngineQueueService } from '@app/shared/queue/assessment-engine-queue.service';
import { BaseResponseDto } from '@app/shared/dto/resp/base-response.dto';
import { ScheduleTestDto } from '@app/shared/assessment-engine/dto/request/schedule-test.dto';
import { UpdateScheduleDto } from '@app/shared/assessment-engine/dto/request/update-schedule.dto';
import { DeleteScheduleDto } from '@app/shared/assessment-engine/dto/request/delete-schedule.dto';
import { ICustomRequest } from '@app/shared/dto/custom-request.dts';
import { RedisCacheService } from '@app/shared/cache/redis-cache.service';
import { createHash } from 'crypto';

@Injectable()
export class TestService {
  getResultById(sessionId: string, user: ICustomRequest) {
    return this.assessmentEngineClient.send(EndpointPattern.ASSESSMENT_ENGINE_SERVICE.GET_RESULT, { sessionId, user });
  }
  constructor(
    @Inject('ASSESSMENT_ENGINE_MS') private readonly assessmentEngineClient: ClientProxy,
    private readonly queueService: AssessmentEngineQueueService,
    private readonly cache: RedisCacheService
  ) {}

  getTestById(id: number) {
    return this.assessmentEngineClient.send(EndpointPattern.ASSESSMENT_ENGINE_SERVICE.GET_TEST_BY_ID, { id });
  }
  deleteTest(id: number) {
    return this.assessmentEngineClient.send(EndpointPattern.ASSESSMENT_ENGINE_SERVICE.DELETE_TEST, { id });
  }
  createTest(test: CreateTestDto) {
    return this.assessmentEngineClient.send(EndpointPattern.ASSESSMENT_ENGINE_SERVICE.CREATE_TEST, test);
  }
  getTest(searchDto: SearchDto) {
    return this.assessmentEngineClient.send(EndpointPattern.ASSESSMENT_ENGINE_SERVICE.GET_TEST, searchDto);
  }
  updateTest(assessmentData: UpdateTestDto) {
    return this.assessmentEngineClient.send(EndpointPattern.ASSESSMENT_ENGINE_SERVICE.UPDATE_TEST, assessmentData);
  }

  reEvaluateResult(id: number, businessId: number) {
    return this.assessmentEngineClient.send(EndpointPattern.ASSESSMENT_ENGINE_SERVICE.RE_EVALUATE_RESULT, {
      id,
      businessId,
    });
  }
  getResultByUser(userId: number, testId: number) {
    return this.assessmentEngineClient.send(EndpointPattern.ASSESSMENT_ENGINE_SERVICE.GET_RESULT_BY_USER, {
      userId,
      testId,
    });
  }

  createTestSession(testSession: CreateTestSessionDto) {
    return this.assessmentEngineClient.send(EndpointPattern.ASSESSMENT_ENGINE_SERVICE.CREATE_TEST_SESSION, testSession);
  }

  async storeAnswers(sessionId: string, answers: AnswerEventDto[]) {
    const ttlSecs = 30 * 60; // 30 minutes
    if (!answers || answers.length === 0) return BaseResponseDto.success();

    // Canonicalize and hash entire array for batch-level dedupe
    const canonical = answers
      .map((a) => ({
        questionId: a.questionId,
        ts: typeof a.ts === 'string' ? a.ts : new Date(a.ts).toISOString(),
        selectedOptionIds: Array.isArray(a.selectedOptionIds) ? [...a.selectedOptionIds].sort((x, y) => x - y) : [],
      }))
      .sort((l, r) => l.questionId - r.questionId || (l.ts < r.ts ? -1 : l.ts > r.ts ? 1 : 0));
    const batchHash = sha256(JSON.stringify(canonical));
    const batchKey = `ans_batch:${sessionId}:${batchHash}`;
    const seenBatch = await this.cache.get<string>(batchKey);
    if (seenBatch) return BaseResponseDto.success();
    await this.cache.set(batchKey, '1', ttlSecs);

    await this.queueService.send(EndpointPattern.ASSESSMENT_ENGINE_SERVICE.STORE_ANSWERS, { sessionId, answers });

    return BaseResponseDto.success();
  }

  async storeProctorEvents(sessionId: string, events: ProctorEventDto[]) {
    const ttlSecs = 30 * 60; // 30 minutes
    if (!events || events.length === 0) return BaseResponseDto.success();

    // Canonicalize and hash entire array for batch-level dedupe
    const canonical = events
      .map((e) => {
        const ts = typeof e.ts === 'string' ? e.ts : new Date(e.ts).toISOString();
        const payloadStr = e.payload == null ? '' : stableStringify(e.payload);
        const payloadHash = payloadStr ? sha256(payloadStr) : '';
        return { eventType: e.eventType, ts, payloadHash };
      })
      .sort(
        (l, r) =>
          (l.eventType < r.eventType ? -1 : l.eventType > r.eventType ? 1 : 0) ||
          (l.ts < r.ts ? -1 : l.ts > r.ts ? 1 : 0) ||
          (l.payloadHash < r.payloadHash ? -1 : l.payloadHash > r.payloadHash ? 1 : 0)
      );
    const batchHash = sha256(JSON.stringify(canonical));
    const batchKey = `proc_batch:${sessionId}:${batchHash}`;
    const seenBatch = await this.cache.get<string>(batchKey);
    if (seenBatch) return BaseResponseDto.success();

    await this.queueService.send(EndpointPattern.ASSESSMENT_ENGINE_SERVICE.STORE_PROCTOR_EVENTS, { sessionId, events });
    await this.cache.set(batchKey, '1', ttlSecs);
    return BaseResponseDto.success();
  }

  getProctorEvents(sessionId: string) {
    return this.assessmentEngineClient.send(EndpointPattern.ASSESSMENT_ENGINE_SERVICE.GET_PROCTOR_EVENTS, {
      sessionId,
    });
  }

  endTestSession(sessionId: string) {
    return this.assessmentEngineClient.send(EndpointPattern.ASSESSMENT_ENGINE_SERVICE.END_TEST_SESSION, sessionId);
  }

  scheduleTest(dto: ScheduleTestDto) {
    return this.assessmentEngineClient.send(EndpointPattern.ASSESSMENT_ENGINE_SERVICE.SCHEDULE_TEST, dto);
  }

  validateSchedule(id: number, email: string) {
    return this.assessmentEngineClient.send(EndpointPattern.ASSESSMENT_ENGINE_SERVICE.VALIDATE_SCHEDULE, { id, email });
  }

  getSchedules(user: ICustomRequest) {
    return this.assessmentEngineClient.send(EndpointPattern.ASSESSMENT_ENGINE_SERVICE.GET_SCHEDULES, user);
  }

  updateSchedules(dto: UpdateScheduleDto) {
    return this.assessmentEngineClient.send(EndpointPattern.ASSESSMENT_ENGINE_SERVICE.UPDATE_SCHEDULES, dto);
  }

  deleteSchedules(dto: DeleteScheduleDto) {
    return this.assessmentEngineClient.send(EndpointPattern.ASSESSMENT_ENGINE_SERVICE.DELETE_SCHEDULES, dto);
  }
}

function sha256(s: string): string {
  return createHash('sha256').update(s).digest('hex');
}

function stableStringify(value: any): string {
  return JSON.stringify(sortDeep(value));
}

function sortDeep(input: any): any {
  if (Array.isArray(input)) {
    return input.map(sortDeep);
  }
  if (input && typeof input === 'object') {
    const sorted: any = {};
    for (const key of Object.keys(input).sort()) {
      sorted[key] = sortDeep(input[key]);
    }
    return sorted;
  }
  return input;
}
