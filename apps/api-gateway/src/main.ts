import { NestFactory } from '@nestjs/core';
import { ApiGatewayModule } from './api-gateway.module';
import { ConfigService } from '@nestjs/config';
import { DocumentBuilder, SwaggerModule } from '@nestjs/swagger';
import { Logger } from 'nestjs-pino';
import { ValidationPipe } from '@nestjs/common';
import { Histogram, Counter } from 'prom-client';
import { GlobalExceptionHandler } from '../../../libs/shared/src/exception/global-exception-handler';
import { AuthGuard } from '../../../libs/shared/src/auth/guard/auth.guard';

const httpRequestDurationMicroseconds = new Histogram({
  name: 'http_request_duration_seconds',
  help: 'Duration of HTTP requests in seconds',
  labelNames: ['method', 'route', 'status_code'],
  buckets: [0.1, 0.3, 1.5, 10], // 100ms, 300ms, 1.5s, 10s
});

const httpRequestsTotal = new Counter({
  name: 'http_requests_total',
  help: 'Total number of HTTP requests',
  labelNames: ['method', 'route', 'status_code'],
});

const httpRequestsErrors = new Counter({
  name: 'http_requests_errors_total',
  help: 'Total number of HTTP error requests (4xx, 5xx)',
  labelNames: ['method', 'route', 'status_code'],
});

async function bootstrap() {
  const app = await NestFactory.create(ApiGatewayModule, { bufferLogs: true });

  const logger = app.get(Logger);

  app.use((req, res, next) => {
    const end = httpRequestDurationMicroseconds.startTimer();
    res.on('finish', () => {
      // Use route pattern instead of full URL for better grouping
      const route = req.route?.path || req.url.split('?')[0]; // Remove query params
      const labels = { method: req.method, route: route, status_code: res.statusCode };

      // Track duration
      end(labels);

      // Track total requests
      httpRequestsTotal.inc(labels);

      // Track errors (4xx and 5xx status codes)
      if (res.statusCode >= 400) {
        httpRequestsErrors.inc(labels);
      }
    });
    next();
  });

  const configService = app.get(ConfigService);
  app.enableCors();
  app.useGlobalPipes(new ValidationPipe({ whitelist: true }));
  app.useLogger(app.get(Logger));
  app.useGlobalFilters(new GlobalExceptionHandler(app.get(Logger)));
  app.setGlobalPrefix('api');
  const config = new DocumentBuilder()
    .setTitle('AptiTest API Gateway')
    .setDescription('Documentation API Gateway')
    .setVersion(configService.get('VERSION') || '1.0.0')
    .addBearerAuth()
    .build();

  const document = SwaggerModule.createDocument(app, config);
  SwaggerModule.setup('api/docs', app, document);
  logger.debug('Swagger documentation is available at http://localhost:3000/api/docs');

  await app.listen(configService.get('GATEWAY_PORT') || 3000);
}
bootstrap();
