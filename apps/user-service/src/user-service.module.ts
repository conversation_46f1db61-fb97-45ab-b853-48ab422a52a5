import { Module } from '@nestjs/common';
import { UserController } from './controller/user.controller';
import { UserService } from './services/user.service';
import { BusinessAccountController } from './controller/business-account.controller';
import { BusinessAccountService } from './services/business-account.service';
import { ConfigModule } from '@nestjs/config';
import { DatabaseModule } from '@app/shared/database/database.module';
import { User } from '@app/shared/user-service/entities/user.entities';
import { ApRole } from '@app/shared/user-service/entities/ap-role.entities';
import { ApPrivilege } from '@app/shared/user-service/entities/ap-privilege.entities';
import { BusinessAccount } from '@app/shared/user-service/entities/business-account.entity';
import { UserRepository } from '@app/shared/user-service/repository/user.repository';
import { BusinessAccountRepository } from '@app/shared/user-service/repository/business-account.repository';
import { SharedModule } from '@app/shared/shared.module';
import { PasswordEncoder } from '@app/shared/util/password-encoder';
import { QueueModule } from '@app/shared/queue/queue.module';

@Module({
  imports: [
    ConfigModule.forRoot({
      isGlobal: true,
      envFilePath: './.env',
    }),
    DatabaseModule.forFeature([User, ApRole, ApPrivilege, BusinessAccount]),
    QueueModule,
    SharedModule,
  ],
  controllers: [UserController, BusinessAccountController],
  providers: [UserService, BusinessAccountService, UserRepository, BusinessAccountRepository, PasswordEncoder],
})
export class UserServiceModule {}
