import { Injectable } from '@nestjs/common';
import { BusinessAccountCreation } from '@app/shared/dto/request/business-account.dto';
import { BusinessAccountRepository } from '@app/shared/user-service/repository/business-account.repository';
import { CustomException } from '@app/shared/exception/custom.exception';
import { BusinessAccount } from '@app/shared/user-service/entities/business-account.entity';
import { ResponseDto } from '@app/shared/dto/resp/base-response.dto';
import { UserRepository } from '@app/shared/user-service/repository/user.repository';
import { PasswordEncoder } from '@app/shared/util/password-encoder';
import { NotificationQueueService } from '@app/shared/queue/notification-queue.service';
import { EndpointPattern } from '@app/shared/util/endpoint-pattern';
import { EmailTemplate } from '@app/shared/notification-ms/email-template.dto';

@Injectable()
export class BusinessAccountService {
  constructor(
    private readonly businessAccountRepository: BusinessAccountRepository,
    private readonly userRepository: UserRepository,
    private readonly passwordEncoder: PasswordEncoder,
    private readonly queueService: NotificationQueueService
  ) {}

  async onboardBusiness(payload: BusinessAccountCreation): Promise<ResponseDto<BusinessAccount>> {
    const email = payload.businessCredentials.email;
    const existing = await this.businessAccountRepository.findOneBy({ email });
    if (existing) {
      throw new CustomException('Business already exist');
    }

    const userExist = await this.userRepository.findOneBy({ email });
    if (userExist) {
      throw new CustomException('User already exist');
    }

    const businessData: Partial<BusinessAccount> = {
      name: payload.businessCredentials.name,
      email: email,
      phoneNumber: payload.businessCredentials.phoneNumber,
      address: payload.businessCredentials.address,
      city: payload.businessCredentials.city,
      country: payload.businessCredentials.country,
      primaryColor: payload.themeData.primaryColor,
      secondaryColor: payload.themeData.secondaryColor,
      logo: payload.themeData.logoBase64,
      plan: payload.subscription.plan,
      startDate: payload.subscription.startDate,
      endDate: payload.subscription.endDate,
      isActive: payload.subscription.isActive,
      subscriptionId: payload.subscription.subscriptionId,
    };

    const business = await this.businessAccountRepository.createEntity(businessData);

    // Create default user for business
    const hashedPassword = await this.passwordEncoder.encode('ChangeMe123!');
    await this.userRepository.createEntity({
      firstName: payload.businessCredentials.name,
      lastName: payload.businessCredentials.name,
      email: email,
      password: hashedPassword,
      businessId: business.id,
    });

    // Send welcome notification to business owner
    try {
      await this.queueService.send(EndpointPattern.NOTIFICATION_SERVICE.SEND_NOTIFICATION, {
        to: [email],
        subject: EmailTemplate.WELCOME_BUSINESS.emailSubject,
        template: 'WELCOME_BUSINESS',
        param: {
          name: payload.businessCredentials.name,
        },
      });
    } catch (error) {
      // Non-blocking: onboarding succeeds even if email fails
    }

    return new ResponseDto(business);
  }
}
