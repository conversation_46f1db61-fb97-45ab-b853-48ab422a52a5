import { Injectable } from '@nestjs/common';
import { UserSignupDto } from '@app/shared/dto/user-signup.dto';
import { BaseResponseDto, ResponseDto } from '@app/shared/dto/resp/base-response.dto';
import { UserRepository } from '@app/shared/user-service/repository/user.repository';
import { UserSigninDto } from '@app/shared/dto/user-signin.dto';
import { PasswordEncoder } from '@app/shared/util/password-encoder';
import { AuthService } from '@app/shared/auth/services/auth.lib.service';
import { AccessDto } from '@app/shared/dto/resp/access.dto';
import { RpcException } from '@nestjs/microservices';
import { CustomException } from '@app/shared/exception/custom.exception';
import { EndpointPattern } from '@app/shared/util/endpoint-pattern';
import { PinoLogger } from 'nestjs-pino';
import { NotificationQueueService } from '@app/shared/queue/notification-queue.service';
import { EmailTemplate } from '@app/shared/notification-ms/email-template.dto';
import { User } from '@app/shared/user-service/entities/user.entities';
import { GoogleAuthDto } from '@app/shared/dto/google-auth.dto';
import {
  BusinessUserIdDto,
  CreateBusinessUserDto,
  UpdateBusinessUserDto,
} from '@app/shared/dto/request/business-user.dto';
import { BusinessAccountRepository } from '@app/shared/user-service/repository/business-account.repository';

@Injectable()
export class UserService {
  constructor(
    private readonly userRepository: UserRepository,
    private readonly passwordEncoder: PasswordEncoder,
    private readonly authService: AuthService,
    private readonly queueService: NotificationQueueService,
    private readonly logger: PinoLogger,
    private readonly businessAccountRepository: BusinessAccountRepository
  ) {}

  async signup(payload: UserSignupDto): Promise<ResponseDto<AccessDto>> {
    if (payload.password !== payload.confirmPassword) {
      throw new CustomException('Password does not match');
    }
    const userExist = await this.userRepository.findOneBy({ email: payload.email });
    if (userExist) {
      throw new CustomException('User already exist');
    }

    const hashedPassword = await this.passwordEncoder.encode(payload.password);
    const user = await this.userRepository.createEntity({ ...payload, password: hashedPassword });

    // Send welcome notification
    try {
      await this.queueService.send(EndpointPattern.NOTIFICATION_SERVICE.SEND_NOTIFICATION, {
        to: [payload.email],
        template: EmailTemplate.WELCOME,
        param: {
          name: payload.firstName ?? payload.email,
        },
      });
    } catch (error) {
      this.logger.error('Error sending notification: ', error);
    }

    return await this.signInDetials(user);
  }

  async signInWithPassword(payload: UserSigninDto): Promise<ResponseDto<AccessDto>> {
    const user = await this.userRepository.findOneBy({ email: payload.email });
    if (!user) {
      throw new CustomException('Invalid username or password!');
    }

    const isPasswordValid = await this.passwordEncoder.compare(payload.password, user.password);
    if (!isPasswordValid) {
      throw new CustomException('Invalid username or password!');
    }

    return await this.signInDetials(user);
  }

  async signInWithGoogle(payload: GoogleAuthDto): Promise<ResponseDto<AccessDto>> {
    const ticket = await this.authService.verifyGoogleToken(payload.token);
    if (!ticket || ticket.email !== payload.email) {
      throw new CustomException('Invalid Google token');
    }

    let user = await this.userRepository.findOneBy({ email: payload.email });
    if (!user) {
      const randomPassword = await this.passwordEncoder.encode(Math.random().toString(36).slice(-8));
      user = await this.userRepository.createEntity({
        email: payload.email,
        firstName: payload.firstName ?? ticket.given_name,
        lastName: payload.lastName ?? ticket.family_name,
        password: randomPassword,
      });
    }

    return await this.signInDetials(user);
  }

  async signOut(token: string): Promise<BaseResponseDto> {
    await this.authService.invalidateToken(token);

    return BaseResponseDto.success('User signed out successfully');
  }

  async createBusinessUser(payload: CreateBusinessUserDto): Promise<ResponseDto<User>> {
    const business = await this.businessAccountRepository.findOneBy({ id: payload.businessId });
    if (!business) {
      throw new CustomException('Business not found');
    }

    const existing = await this.userRepository.findOneBy({ email: payload.email });
    if (existing) {
      throw new CustomException('User already exist');
    }

    const hashedPassword = await this.passwordEncoder.encode(payload.password);
    const user = await this.userRepository.createEntity({
      businessId: payload.businessId,
      firstName: payload.firstName,
      lastName: payload.lastName,
      email: payload.email,
      password: hashedPassword,
    });

    return new ResponseDto(user);
  }

  async updateBusinessUser(payload: UpdateBusinessUserDto): Promise<ResponseDto<User>> {
    const business = await this.businessAccountRepository.findOneBy({ id: payload.businessId });
    if (!business) {
      throw new CustomException('Business not found');
    }

    const user = await this.userRepository.findOneBy({ id: payload.userId });
    if (!user || user.businessId !== payload.businessId) {
      throw new CustomException('User not found');
    }

    const updateData: Partial<User> = {};
    if (payload.firstName) updateData.firstName = payload.firstName;
    if (payload.lastName) updateData.lastName = payload.lastName;
    if (payload.email) updateData.email = payload.email;
    if (payload.password) {
      updateData.password = await this.passwordEncoder.encode(payload.password);
    }

    await this.userRepository.updateEntity(user.id, updateData);
    const updated = await this.userRepository.findOneBy({ id: user.id });
    return new ResponseDto(updated!);
  }

  async deleteBusinessUser(payload: BusinessUserIdDto): Promise<BaseResponseDto> {
    const user = await this.userRepository.findOneBy({ id: payload.userId });
    if (!user || user.businessId !== payload.businessId) {
      throw new CustomException('User not found');
    }

    await this.userRepository.updateEntity(user.id, { active: false, deleted: true });
    return BaseResponseDto.success('User deleted successfully');
  }

  async lockBusinessUser(payload: BusinessUserIdDto): Promise<BaseResponseDto> {
    const user = await this.userRepository.findOneBy({ id: payload.userId });
    if (!user || user.businessId !== payload.businessId) {
      throw new CustomException('User not found');
    }

    await this.userRepository.updateEntity(user.id, { active: false });
    return BaseResponseDto.success('User locked successfully');
  }

  async refreshToken(token: string): Promise<ResponseDto<AccessDto>> {
    const payload = this.authService.verifyToken(token);
    if (!payload || !payload?.refreshToken) {
      throw new RpcException('Invalid token. Please log in again.');
    }

    const user = await this.userRepository.findOneBy({ email: payload.email });
    if (!user) {
      throw new RpcException('User not found. Please log in again.');
    }

    return await this.signInDetials(user);
  }

  async signInDetials(user: User): Promise<ResponseDto<AccessDto>> {
    const roles = await user.roles;
    const privileges = await roles?.map((role) => role.privileges).flatMap((p) => p);

    const jwtPayload = {
      email: user.email,
      userId: user.id,
      role: roles?.map((r) => r.roleName),
      privileges: privileges?.map((p) => p.name),
      businessId: user.businessId,
    };
    const token = this.authService.generateToken(jwtPayload);
    const refreshToken = this.authService.generateToken({ refreshToken: true });

    const accessDto = new AccessDto();
    accessDto.token = token;
    accessDto.refreshToken = refreshToken;
    accessDto.expiresIn = 3600;
    accessDto.refreshTokenExpiresIn = 3600;
    accessDto.roles = roles.map((r) => r.roleName);
    accessDto.privileges = privileges.map((p) => p.name);
    accessDto.email = user.email;
    accessDto.firstName = user.firstName;
    accessDto.lastName = user.lastName;
    accessDto.businessId = user.businessId;

    return new ResponseDto(accessDto);
  }
}
