import { Controller, Get } from '@nestjs/common';
import { UserService } from '../services/user.service';
import { MessagePattern } from '@nestjs/microservices';
import { UserSignupDto } from '@app/shared/dto/user-signup.dto';
import { BaseResponseDto, ResponseDto } from '@app/shared/dto/resp/base-response.dto';
import { EndpointPattern } from '@app/shared/util/endpoint-pattern';
import { UserSigninDto } from '@app/shared/dto/user-signin.dto';
import { GoogleAuthDto } from '@app/shared/dto/google-auth.dto';
import { AccessDto } from '@app/shared/dto/resp/access.dto';
import {
  BusinessUserIdDto,
  CreateBusinessUserDto,
  UpdateBusinessUserDto,
} from '@app/shared/dto/request/business-user.dto';
import { User } from '@app/shared/user-service/entities/user.entities';

@Controller()
export class UserController {
  constructor(private readonly userService: UserService) {}

  @MessagePattern(EndpointPattern.USER_SERVICE.SIGNUP)
  async signup(payload: UserSignupDto): Promise<ResponseDto<AccessDto>> {
    return await this.userService.signup(payload);
  }

  @MessagePattern(EndpointPattern.USER_SERVICE.SIGNIN)
  async signInWithPassword(payload: UserSigninDto): Promise<ResponseDto<AccessDto>> {
    return await this.userService.signInWithPassword(payload);
  }

  @MessagePattern(EndpointPattern.USER_SERVICE.GOOGLE_AUTH)
  async signInWithGoogle(payload: GoogleAuthDto): Promise<ResponseDto<AccessDto>> {
    return await this.userService.signInWithGoogle(payload);
  }

  @MessagePattern(EndpointPattern.USER_SERVICE.SIGNOUT)
  async signOut(token: string): Promise<BaseResponseDto> {
    return await this.userService.signOut(token);
  }

  @MessagePattern(EndpointPattern.USER_SERVICE.CREATE_USER)
  async createBusinessUser(payload: CreateBusinessUserDto): Promise<ResponseDto<User>> {
    return await this.userService.createBusinessUser(payload);
  }

  @MessagePattern(EndpointPattern.USER_SERVICE.UPDATE_USER)
  async updateBusinessUser(payload: UpdateBusinessUserDto): Promise<ResponseDto<User>> {
    return await this.userService.updateBusinessUser(payload);
  }

  @MessagePattern(EndpointPattern.USER_SERVICE.DELETE_USER)
  async deleteBusinessUser(payload: BusinessUserIdDto): Promise<BaseResponseDto> {
    return await this.userService.deleteBusinessUser(payload);
  }

  @MessagePattern(EndpointPattern.USER_SERVICE.LOCK_USER)
  async lockBusinessUser(payload: BusinessUserIdDto): Promise<BaseResponseDto> {
    return await this.userService.lockBusinessUser(payload);
  }
}
