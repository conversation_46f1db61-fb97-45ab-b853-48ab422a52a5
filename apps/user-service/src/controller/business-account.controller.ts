import { Controller } from '@nestjs/common';
import { MessagePattern } from '@nestjs/microservices';
import { BusinessAccountCreation } from '@app/shared/dto/request/business-account.dto';
import { EndpointPattern } from '@app/shared/util/endpoint-pattern';
import { BusinessAccountService } from '../services/business-account.service';

@Controller()
export class BusinessAccountController {
  constructor(private readonly businessAccountService: BusinessAccountService) {}

  @MessagePattern(EndpointPattern.USER_SERVICE.ONBOARD_BUSINESS)
  async onboardBusiness(payload: BusinessAccountCreation) {
    return await this.businessAccountService.onboardBusiness(payload);
  }
}
