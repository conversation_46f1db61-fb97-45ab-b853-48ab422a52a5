import { Injectable, InternalServerErrorException } from '@nestjs/common';
import { EmailNotificationDto } from '@app/shared/notification-ms/email-notification.dto';
import { MailerService } from '@nestjs-modules/mailer';
import * as fs from 'fs';
import { EmailTemplate } from '@app/shared/notification-ms/email-template.dto';
import { PinoLogger } from 'nestjs-pino';
import ejs from 'ejs';

import { ConfigService } from '@nestjs/config';
import { BaseResponseDto } from '@app/shared/dto/resp/base-response.dto';

@Injectable()
export class NotificationMsService {
  constructor(
    private readonly mailerService: MailerService,
    private readonly configService: ConfigService,
    private readonly logger: PinoLogger
  ) {}

  async sendNotification(data: EmailNotificationDto & { content: string }) {
    try {
      this.logger.info('Sending notification: ', data);
      data.to.forEach((to) => {
        this.logger.info('Sending notification to: ', to);
        this.mailerService.sendMail({
          to,
          subject: data.subject,
          html: data.content,
        });
      });
    } catch (error) {
      this.logger.error('Error sending notification: ', error);
    }
  }

  async sendEmailNotificationTemplate(data: EmailNotificationDto): Promise<BaseResponseDto> {
    const response = new BaseResponseDto();

    const message = await this.generateMessage(data);

    if (!message) {
      throw new InternalServerErrorException(`${data.template} email template not found`);
    }

    await this.sendNotification({ ...data, content: message });

    return response;
  }

  generateMessage(data: EmailNotificationDto): string {
    let message = '';
    let emailTemplate = EmailTemplate[data.template];
    try {
      message = fs.readFileSync(emailTemplate.messagePath, 'utf8');
    } catch (error) {
      this.logger.error(
        `Template file not found at: ${emailTemplate.messagePath} \n error:  ${error instanceof Error ? error.stack : error}`
      );
      throw new InternalServerErrorException(`Error occurred while generating message`);
    }

    if (emailTemplate.messagePath.endsWith('.ejs')) {
      data.param['title'] = emailTemplate.emailSubject;
      data.param['notificationBaseUrl'] = this.configService.get<string>('NOTIFICATION_BASE_URL');
      data.param['loginLink'] = this.configService.get<string>('LOGIN_BASE_URL');
      data.param['supportEmail'] = this.configService.get<string>('SUPPORT_EMAIL_ADDRESS');
      message = ejs.render(message, data.param, { views: [EmailTemplate.VIEWS_INCLUDE_PATH.messagePath] });
    }

    return message;
  }
}
