import { Controller, Get } from '@nestjs/common';
import { NotificationMsService } from './notification-ms.service';
import { EventPattern } from '@nestjs/microservices';
import { EndpointPattern } from '../../../libs/shared/src/util/endpoint-pattern';
import { EmailNotificationDto } from '../../../libs/shared/src/notification-ms/email-notification.dto';

@Controller()
export class NotificationMsController {
  constructor(private readonly notificationMsService: NotificationMsService) {}

  @EventPattern(EndpointPattern.NOTIFICATION_SERVICE.SEND_NOTIFICATION)
  async sendNotification(data: EmailNotificationDto) {
    console.log('Received notification data:', data);
    await this.notificationMsService.sendEmailNotificationTemplate(data);
  }
}
