import { Module } from '@nestjs/common';
import { NotificationMsController } from './notification-ms.controller';
import { NotificationMsService } from './notification-ms.service';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { QueueModule } from '../../../libs/shared/src/queue/queue.module';
import { MailerModule } from '@nestjs-modules/mailer';

@Module({
  imports: [
    QueueModule,
    MailerModule.forRootAsync({
      imports: [ConfigModule],
      useFactory: async (configService: ConfigService) => ({
        transport: {
          host: configService.get('SMTP_HOST'),
          port: configService.get('SMTP_PORT'),
          auth: {
            user: configService.get('SMTP_USER'),
            pass: configService.get('SMTP_PASS'),
          },
        },
      }),
      inject: [ConfigService],
    }),
  ],
  controllers: [NotificationMsController],
  providers: [NotificationMsService],
})
export class NotificationMsModule {}
