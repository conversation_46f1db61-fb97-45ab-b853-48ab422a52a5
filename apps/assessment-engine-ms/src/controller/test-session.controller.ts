import { Controller, UseInterceptors } from '@nestjs/common';
import { EventPattern, MessagePattern, Payload } from '@nestjs/microservices';
import { ICustomRequest } from '@app/shared/dto/custom-request.dts';

import { EndpointPattern } from '@app/shared/util/endpoint-pattern';
import { AnswerEventDto, CreateTestSessionDto } from '@app/shared/assessment-engine/dto/request/test-session.dto';
import { ProctorEventDto } from '@app/shared/assessment-engine/dto/request/proctor-event.dto';
import { TestSessionService } from '../services/test-session.service';
import { RmqAckInterceptor } from '@app/shared/interceptors/rmq-exception.interceptor';

@Controller()
export class TestSessionController {
  constructor(private readonly testSessionService: TestSessionService) {}

  @MessagePattern(EndpointPattern.ASSESSMENT_ENGINE_SERVICE.CREATE_TEST_SESSION)
  async createTestSession(data: CreateTestSessionDto) {
    return await this.testSessionService.createTestSession(data);
  }

  /**
   * This is coming from the queue
   * **/
  @UseInterceptors(RmqAckInterceptor)
  @EventPattern(EndpointPattern.ASSESSMENT_ENGINE_SERVICE.STORE_ANSWERS)
  async storeAnswers(@Payload() data: { sessionId: string; answers: AnswerEventDto[] }) {
    return await this.testSessionService.storeAnswers(data.sessionId, data.answers);
  }

  @UseInterceptors(RmqAckInterceptor)
  @EventPattern(EndpointPattern.ASSESSMENT_ENGINE_SERVICE.STORE_PROCTOR_EVENTS)
  async storeProctorEvents(@Payload() data: { sessionId: string; events: ProctorEventDto[] }) {
    return await this.testSessionService.storeProctorEvents(data.sessionId, data.events);
  }

  @MessagePattern(EndpointPattern.ASSESSMENT_ENGINE_SERVICE.END_TEST_SESSION)
  async endTestSession(sessionId: string) {
    return await this.testSessionService.endTestSession(sessionId);
  }

  @UseInterceptors(RmqAckInterceptor)
  @EventPattern(EndpointPattern.ASSESSMENT_ENGINE_SERVICE.PROCESS_RESULT)
  async processResult(@Payload() data: { sessionId: string }) {
    return await this.testSessionService.processResult(data.sessionId);
  }

  @MessagePattern(EndpointPattern.ASSESSMENT_ENGINE_SERVICE.GET_RESULT)
  async getResult(data: { sessionId: string; user: ICustomRequest }) {
    return await this.testSessionService.getResult(data.sessionId, data.user);
  }

  @MessagePattern(EndpointPattern.ASSESSMENT_ENGINE_SERVICE.GET_PROCTOR_EVENTS)
  async getProctorEvents(data: { sessionId: string }) {
    return await this.testSessionService.getProctorEvents(data.sessionId);
  }
}
