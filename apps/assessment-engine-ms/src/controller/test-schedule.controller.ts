import { Controller } from '@nestjs/common';
import { MessagePattern } from '@nestjs/microservices';
import { EndpointPattern } from '@app/shared/util/endpoint-pattern';
import { ScheduleTestDto } from '@app/shared/assessment-engine/dto/request/schedule-test.dto';
import { UpdateScheduleDto } from '@app/shared/assessment-engine/dto/request/update-schedule.dto';
import { DeleteScheduleDto } from '@app/shared/assessment-engine/dto/request/delete-schedule.dto';
import { ICustomRequest } from '@app/shared/dto/custom-request.dts';

import { TestScheduleService } from '../services/test-schedule.service';

@Controller()
export class TestScheduleController {
  constructor(private readonly testScheduleService: TestScheduleService) {}

  @MessagePattern(EndpointPattern.ASSESSMENT_ENGINE_SERVICE.SCHEDULE_TEST)
  scheduleTest(data: ScheduleTestDto) {
    return this.testScheduleService.scheduleTest(data);
  }

  @MessagePattern(EndpointPattern.ASSESSMENT_ENGINE_SERVICE.VALIDATE_SCHEDULE)
  validateSchedule(data: { id: number; email: string }) {
    return this.testScheduleService.validateSchedule(data.id, data.email);
  }

  @MessagePattern(EndpointPattern.ASSESSMENT_ENGINE_SERVICE.GET_SCHEDULES)
  getSchedules(data: ICustomRequest) {
    return this.testScheduleService.getSchedules(data);
  }

  @MessagePattern(EndpointPattern.ASSESSMENT_ENGINE_SERVICE.UPDATE_SCHEDULES)
  updateSchedules(data: UpdateScheduleDto) {
    return this.testScheduleService.updateSchedules(data);
  }

  @MessagePattern(EndpointPattern.ASSESSMENT_ENGINE_SERVICE.DELETE_SCHEDULES)
  deleteSchedules(data: DeleteScheduleDto) {
    return this.testScheduleService.deleteSchedules(data);
  }
}
