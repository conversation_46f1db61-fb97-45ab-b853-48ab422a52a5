import { Controller } from '@nestjs/common';
import { MessagePattern } from '@nestjs/microservices';
import { EndpointPattern } from '@app/shared/util/endpoint-pattern';
import { CreateTestDto, UpdateTestDto } from '@app/shared/assessment-engine/dto/request/question.dto';
import { TestService } from '../services/test.service';
import { SearchDto } from '@app/shared/dto/request/search.dto';

@Controller()
export class TestController {
  constructor(private readonly testService: TestService) {}

  @MessagePattern(EndpointPattern.ASSESSMENT_ENGINE_SERVICE.CREATE_TEST)
  async createTest(data: CreateTestDto) {
    return await this.testService.createTest(data);
  }
  @MessagePattern(EndpointPattern.ASSESSMENT_ENGINE_SERVICE.GET_TEST)
  async getTest(data: SearchDto) {
    return await this.testService.getTest(data);
  }
  @MessagePattern(EndpointPattern.ASSESSMENT_ENGINE_SERVICE.UPDATE_TEST)
  async updateTest(data: UpdateTestDto) {
    return await this.testService.updateTest(data);
  }
  @MessagePattern(EndpointPattern.ASSESSMENT_ENGINE_SERVICE.DELETE_TEST)
  async deleteTest(id: number) {
    return await this.testService.deleteTest(id);
  }
  @MessagePattern(EndpointPattern.ASSESSMENT_ENGINE_SERVICE.GET_TEST_BY_ID)
  async getTestById(id: number) {
    return await this.testService.getTestById(id);
  }

  @MessagePattern(EndpointPattern.ASSESSMENT_ENGINE_SERVICE.RE_EVALUATE_RESULT)
  async reEvaluateResult(id: number) {
    return await this.testService.reEvaluateResult(id);
  }
  @MessagePattern(EndpointPattern.ASSESSMENT_ENGINE_SERVICE.GET_RESULT_BY_USER)
  async getResultByUser(data: { userId: number; testId: number }) {
    return await this.testService.getResultByUser(data.userId, data.testId);
  }
}
