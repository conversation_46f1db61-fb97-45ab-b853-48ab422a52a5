import { Controller, Get } from '@nestjs/common';
import { AssessmentEngineService } from '../services/assessment-engine.service';
import { MessagePattern } from '@nestjs/microservices';
import { EndpointPattern } from '@app/shared/util/endpoint-pattern';
import { CreateAssessmentDto, UpdateAssessmentDto } from '@app/shared/assessment-engine/dto/request/assessment.dto';

export class AssessmentEngineMsController {
  constructor(private readonly assessmentEngineMsService: AssessmentEngineService) {}

  @MessagePattern(EndpointPattern.ASSESSMENT_ENGINE_SERVICE.CREATE_ASSESSMENT)
  async createAssessment(data: CreateAssessmentDto) {
    return await this.assessmentEngineMsService.createAssessment(data);
  }

  @MessagePattern(EndpointPattern.ASSESSMENT_ENGINE_SERVICE.GET_ASSESSMENT)
  async getAssessment(data: { name?: string; date?: string }) {
    return await this.assessmentEngineMsService.getAssessment(data.name, data.date);
  }

  @MessagePattern(EndpointPattern.ASSESSMENT_ENGINE_SERVICE.UPDATE_ASSESSMENT)
  async updateAssessment(data: UpdateAssessmentDto) {
    return await this.assessmentEngineMsService.updateAssessment(data);
  }

  @MessagePattern(EndpointPattern.ASSESSMENT_ENGINE_SERVICE.DELETE_ASSESSMENT)
  async deleteAssessment(id: number) {
    return await this.assessmentEngineMsService.deleteAssessment(id);
  }
}
