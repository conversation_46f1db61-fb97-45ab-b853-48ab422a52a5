import { Injectable } from '@nestjs/common';
import { CreateTestDto, UpdateTestDto } from '@app/shared/assessment-engine/dto/request/question.dto';
import { TestRepository } from '@app/shared/assessment-engine/repository/test.repository';
import { AptiTest } from '@app/shared/assessment-engine/entities/test.entity';
import { AptiQuestion } from '@app/shared/assessment-engine/entities/question.entity';
import { AptiOption } from '@app/shared/assessment-engine/entities/option.entity';
import { BaseResponseDto, ResponseDto } from '@app/shared/dto/resp/base-response.dto';
import { CustomException } from '@app/shared/exception/custom.exception';
import { TestResultRepository } from '@app/shared/assessment-engine/repository/test-result.repository';
import { SearchDto } from '@app/shared/dto/request/search.dto';

@Injectable()
export class TestService {
  constructor(
    private readonly testRepository: TestRepository,
    private readonly testResultRepository: TestResultRepository
  ) {}

  async deleteTest(id: number) {
    const test = await this.testRepository.findOne({ id });
    if (!test) {
      return BaseResponseDto.success();
    }
    test.deleted = true;
    test.active = false;

    this.testRepository.updateEntity(test.id, test);
    return true;
  }
  async updateTest(data: UpdateTestDto) {
    const test = await this.testRepository.findOne({ id: data.id });
    if (!test) {
      throw new CustomException('Test not found');
    }
    test.testName = data.testName;
    test.description = data.description;
    test.duration = data.duration;
    test.type = data.type;

    this.testRepository.updateEntity(test.id, test);
    return BaseResponseDto.success();
  }
  async getTest(searchDto: SearchDto): Promise<ResponseDto<AptiTest[]>> {
    const resp = await this.testRepository.getTestForDataTable(searchDto);
    return new ResponseDto(resp);
  }
  async createTest(data: CreateTestDto) {
    const test = new AptiTest({});
    test.testName = data.testName;
    test.description = data.description;
    test.duration = data.duration;
    test.createdBy = data.author.email;
    test.type = data.type;

    // create questions
    const questions = [];
    for (const question of data.questions) {
      const aptiQuestion = new AptiQuestion({});
      aptiQuestion.questionText = question.question;
      aptiQuestion.questionType = question.type;

      aptiQuestion.test = test;
      aptiQuestion.imageUrl = question.imageUrl;
      aptiQuestion.options = question.options.map((option) => {
        const aptiOption = new AptiOption({});
        aptiOption.value = option.option;
        aptiOption.isCorrect = option.isCorrect;
        aptiOption.imageUrl = option.imageUrl;
        return aptiOption;
      });

      questions.push(aptiQuestion);
    }

    test.questions = questions;

    await this.testRepository.createEntity(test);

    return BaseResponseDto.success();
  }

  async getTestById(id: number) {
    const test = await this.testRepository.findOne({ id });
    if (!test) {
      throw new Error('Test not found');
    }
    return test;
  }

  async getResultByUser(userId: number, testId: number) {
    const result = await this.testResultRepository.findOne({ userId, testId });
    if (!result) {
      throw new CustomException('Result not found');
    }
    return new ResponseDto(result);
  }

  reEvaluateResult(id: number) {
    throw new Error('Method not implemented.');
  }
}
