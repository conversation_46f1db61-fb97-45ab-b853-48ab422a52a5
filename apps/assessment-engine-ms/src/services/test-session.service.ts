import { AnswerEventDto, CreateTestSessionDto } from '@app/shared/assessment-engine/dto/request/test-session.dto';
import { TestSessionRepository } from '@app/shared/assessment-engine/repository/test-session.repository';
import { TestSession } from '@app/shared/assessment-engine/entities/test-session.entity';
import { TestSessionStatus } from '@app/shared/assessment-engine/dto/test-session-status.enum';
import { Injectable } from '@nestjs/common';
import { CustomException } from '@app/shared/exception/custom.exception';
import { TestRepository } from '@app/shared/assessment-engine/repository/test.repository';
import { AssessmentRepository } from '@app/shared/assessment-engine/repository/assessment.repository';
import { BaseResponseDto, ResponseDto } from '@app/shared/dto/resp/base-response.dto';
import { TestSessionCreationResponseDto } from '@app/shared/assessment-engine/dto/response/test-session-creation.dto';
import { RedisCacheService } from '@app/shared/cache/redis-cache.service';
import { v4 as uuid4 } from 'uuid';
import { AptiQuestion } from '@app/shared/assessment-engine/entities/question.entity';
import { TestQuestions } from '@app/shared/assessment-engine/dto/response/test-session-creation.dto';
import { QuestionRepository } from '@app/shared/assessment-engine/repository/question.repository';
import { AnswerEventRepository } from '../../../../libs/shared/src/assessment-engine/repository/answer-event.repository';
import { ProctorEventRepository } from '@app/shared/assessment-engine/repository/proctor-event.repository';
import { ProctorEventEntity } from '@app/shared/assessment-engine/entities/proctor-event.entity';
import { ProctorEventDto } from '@app/shared/assessment-engine/dto/request/proctor-event.dto';
import { PinoLogger } from 'nestjs-pino';
import { AssessmentEngineQueueService } from '@app/shared/queue/assessment-engine-queue.service';
import { EndpointPattern } from '@app/shared/util/endpoint-pattern';
import { TestResultRepository } from '@app/shared/assessment-engine/repository/test-result.repository';
import { TestResult } from '@app/shared/assessment-engine/entities/test-result.entity';
import { ICustomRequest } from '@app/shared/dto/custom-request.dts';

@Injectable()
export class TestSessionService {
  private SESSION_EXPIRY_TIME = 5 * 60 * 1000; // 5 mins
  private SESSION_CACHE_PREFIX = 'test_session_';

  constructor(
    private readonly testSessionRepository: TestSessionRepository,
    private readonly testRepository: TestRepository,
    private readonly assessmentRepository: AssessmentRepository,
    private readonly questionRepository: QuestionRepository,
    private readonly redisCacheService: RedisCacheService,
    private readonly answerEventRepository: AnswerEventRepository,
    private readonly proctorEventRepository: ProctorEventRepository,
    private readonly testResultRepository: TestResultRepository,
    private readonly queueService: AssessmentEngineQueueService,
    private readonly logger: PinoLogger
  ) {}

  async createTestSession(data: CreateTestSessionDto): Promise<ResponseDto<TestSessionCreationResponseDto>> {
    if (!data.testId && !data.assessmentId) {
      throw new CustomException('Both testId and assessmentId cannot be null');
    }

    const testIds = data.testId ? [data.testId] : [];
    const resp = new TestSessionCreationResponseDto();

    if (data.assessmentId) {
      const assessment = await this.assessmentRepository.findOne({ id: data.assessmentId });
      if (!assessment) {
        throw new CustomException('Assessment not found');
      }
      testIds.push(...assessment.test.map((test) => test.id));
      resp.assessment = {
        id: assessment.id,
        name: assessment.name,
        description: assessment.description,
        duration: assessment.duration,
      };

      resp.duration = assessment.duration;
    }

    resp.test = await this.getQuestionsForTests(testIds, data.assessmentId);

    resp.instructions = {
      general: 'General instructions',
      perQuestion: 'Per question instructions',
      timeLimit: 'Time limit instructions',
      nativeLanguage: 'Native language instructions',
    };

    const testSession = new TestSession({});
    testSession.userId = data.author.userId;
    testSession.testId = data.testId;
    testSession.assessmentId = data.assessmentId;
    testSession.status = TestSessionStatus.CREATED;
    testSession.userId = data.author.userId;
    testSession.sessionId = uuid4();
    await this.testSessionRepository.createEntity(testSession);

    this.redisCacheService.set(`${this.SESSION_CACHE_PREFIX}_${testSession.sessionId}`, testSession);

    resp.id = testSession.id;
    resp.status = testSession.status;
    resp.sessionId = testSession.sessionId;

    return new ResponseDto(resp);
  }
  async getQuestionsForTests(testIds: number[], assessmentId: number): Promise<TestQuestions[]> {
    if (assessmentId) {
      const assessmentQuestions = await this.redisCacheService.get<TestQuestions[]>(
        `assessment_${assessmentId}_questions`
      );
      if (assessmentQuestions) {
        return assessmentQuestions;
      }
    } else if (testIds.length === 1) {
      const testQuestions = await this.redisCacheService.get<TestQuestions[]>(`test_${testIds[0]}_questions`);
      if (testQuestions) {
        return testQuestions;
      }
    }

    const questions = await Promise.all(
      testIds.map(async (testId) => {
        const test = await this.testRepository.findOne({ id: testId });

        const testQuestions = new TestQuestions();
        testQuestions.id = test.id;
        testQuestions.duration = test.duration;
        testQuestions.questions = await this.questionRepository.findQuestionByTestId(testId);
        return testQuestions;
      })
    );

    if (assessmentId) {
      this.redisCacheService.set(`assessment_${assessmentId}_questions`, questions);
    } else if (testIds.length === 1) {
      this.redisCacheService.set(`test_${testIds[0]}_questions`, questions);
    }

    return questions;
  }

  async storeAnswers(sessionId: string, answers: AnswerEventDto[]): Promise<void> {
    let testSession = await this.redisCacheService.get<TestSession>(`${this.SESSION_CACHE_PREFIX}_${sessionId}`);
    if (!testSession) {
      testSession = await this.testSessionRepository.findOne({ sessionId });
      if (testSession) {
        this.redisCacheService.set(`${this.SESSION_CACHE_PREFIX}_${sessionId}`, testSession);
      }
    }

    if (!testSession) {
      // most likely an attack
      this.logger.error('Test session not found', sessionId);
      return;
    }

    // check if the expired time is over 5mins
    if (testSession.endTime && testSession.endTime.getTime() - new Date().getTime() < this.SESSION_EXPIRY_TIME) {
      this.logger.error('Test session expired', testSession);
      return;
    }

    this.answerEventRepository.storeAnswers(answers);
  }

  async endTestSession(sessionId: string): Promise<BaseResponseDto> {
    const testSession = await this.testSessionRepository.findOne({ sessionId });
    if (!testSession) {
      throw new CustomException('Test session not found');
    }
    testSession.endTime = new Date();
    testSession.status = TestSessionStatus.COMPLETED;

    await this.testSessionRepository.updateEntity(testSession.id, testSession);

    await this.queueService.send(EndpointPattern.ASSESSMENT_ENGINE_SERVICE.PROCESS_RESULT, {
      sessionId,
    });

    return BaseResponseDto.success();
  }

  async storeProctorEvents(sessionId: string, events: ProctorEventDto[]): Promise<void> {
    let testSession = await this.redisCacheService.get<TestSession>(`${this.SESSION_CACHE_PREFIX}_${sessionId}`);
    if (!testSession) {
      testSession = await this.testSessionRepository.findOne({ sessionId });
      if (testSession) {
        this.redisCacheService.set(`${this.SESSION_CACHE_PREFIX}_${sessionId}`, testSession);
      }
    }

    if (!testSession) {
      this.logger.error('Test session not found for proctor events', sessionId);
      return;
    }

    const mapped = events.map((e) => ({
      sessionId,
      eventType: e.eventType,
      payload: e.payload,
      stampedAt: e.ts,
    }));

    await this.proctorEventRepository.store(mapped);
  }

  async getProctorEvents(sessionId: string) {
    return this.proctorEventRepository.findBySession(sessionId);
  }

  async processResult(sessionId: string): Promise<void> {
    const testSession = await this.testSessionRepository.findOne({ sessionId });
    if (!testSession) {
      this.logger.error('Test session not found for result processing', sessionId);
      return;
    }

    const answers = await this.answerEventRepository.getLatestAnswers(sessionId);
    if (!answers.length) {
      this.logger.error('No answers found for session', sessionId);
      return;
    }

    const questionIds = answers.map((a) => a.questionId);
    const questions = await this.questionRepository.findByIdsWithOptions(questionIds);

    let score = 0;
    for (const question of questions) {
      const ans = answers.find((a) => a.questionId === question.id);
      const options = await question.options;
      const correctOptionIds = options
        ?.filter((o) => o.isCorrect)
        .map((o) => o.id)
        .sort();
      const selected = ans?.selectedOptionIds?.slice().sort();
      if (
        correctOptionIds &&
        selected &&
        correctOptionIds.length === selected.length &&
        correctOptionIds.every((v, i) => v === selected[i])
      ) {
        score++;
      }
    }

    const result = new TestResult({});
    result.userId = testSession.userId;
    result.testId = testSession.testId;
    result.assessmentId = testSession.assessmentId;
    result.sessionId = sessionId;
    result.score = score;
    result.status = 'COMPLETED';

    await this.testResultRepository.createEntity(result);
  }

  async getResult(sessionId: string, user: ICustomRequest): Promise<ResponseDto<TestResult>> {
    const result = await this.testResultRepository.findOne({ sessionId });
    if (!result) {
      throw new CustomException('Result not found');
    }

    if (result.userId !== user.userId) {
      throw new CustomException('Result not found');
    }

    if (result.assessmentId) {
      const assessment = await this.assessmentRepository.findOne({ id: result.assessmentId });
      if (assessment && assessment.businessId && assessment.businessId !== user.businessId) {
        throw new CustomException('Result not found');
      }
    }

    return new ResponseDto(result);
  }
}
