import { Injectable } from '@nestjs/common';
import { CreateAssessmentDto, UpdateAssessmentDto } from '@app/shared/assessment-engine/dto/request/assessment.dto';
import { AssessmentRepository } from '@app/shared/assessment-engine/repository/assessment.repository';
import { Assessment } from '@app/shared/assessment-engine/entities/assessment.entity';
import { BaseResponseDto } from '@app/shared/dto/resp/base-response.dto';
import { CustomException } from '@app/shared/exception/custom.exception';
import { TestRepository } from '@app/shared/assessment-engine/repository/test.repository';

@Injectable()
export class AssessmentEngineService {
  constructor(
    private readonly assessmentRepository: AssessmentRepository,
    private readonly testRepository: TestRepository
  ) {}

  async createAssessment(data: CreateAssessmentDto): Promise<BaseResponseDto> {
    const existingAssessment = await this.assessmentRepository.findOne({
      name: data.name,
      businessId: data.author.businessId,
    });

    if (existingAssessment) {
      throw new CustomException('Assessment already exists');
    }

    const assessment = new Assessment({});
    assessment.name = data.name;
    assessment.duration = data.duration;
    assessment.enableProctoring = data.enableProctoring;
    assessment.businessId = data.author.businessId;
    assessment.createdBy = data.author.email;
    assessment.test = await this.getTests(data.testIds);

    await this.assessmentRepository.createEntity(assessment);

    return BaseResponseDto.success();
  }
  async getAssessment(name: any, date: any) {
    throw new Error('Method not implemented.');
  }
  async deleteAssessment(id: number) {
    const assessment = await this.assessmentRepository.findOne({ id });
    if (!assessment) {
      return BaseResponseDto.success();
    }

    assessment.deleted = true;
    assessment.active = false;
    await this.assessmentRepository.updateEntity(assessment.id, assessment);

    return BaseResponseDto.success();
  }

  async updateAssessment(data: UpdateAssessmentDto) {
    const assessment = await this.assessmentRepository.findOne({ id: data.id });
    if (!assessment) {
      throw new CustomException('Assessment not found');
    }

    assessment.name = data.name;
    assessment.duration = data.duration;
    assessment.enableProctoring = data.enableProctoring;

    const tests = await this.getTests(data.testIds);
    assessment.test = tests;

    await this.assessmentRepository.updateEntity(assessment.id, assessment);

    return BaseResponseDto.success();
  }

  private async getTests(testIds: number[]) {
    const tests = [];
    for (const id of testIds) {
      const testSection = await this.testRepository.findOne({ id });
      if (!testSection) {
        throw new CustomException('Test not found');
      }
      tests.push(testSection);
    }
    return tests;
  }

  /**
   *  Initializes the TestSession with an ID,
   *  retrieves the
   *
   */
  async startAssessment(id: number) {}
}
