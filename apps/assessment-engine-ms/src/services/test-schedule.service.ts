import { Injectable } from '@nestjs/common';
import { ScheduleTestDto } from '@app/shared/assessment-engine/dto/request/schedule-test.dto';
import { TestScheduleRepository } from '@app/shared/assessment-engine/repository/test-schedule.repository';
import { NotificationQueueService } from '@app/shared/queue/notification-queue.service';
import { TestRepository } from '@app/shared/assessment-engine/repository/test.repository';
import { TestSchedule } from '@app/shared/assessment-engine/entities/test-schedule.entity';
import { BaseResponseDto, ResponseDto } from '@app/shared/dto/resp/base-response.dto';
import { CustomException } from '@app/shared/exception/custom.exception';
import { EndpointPattern } from '@app/shared/util/endpoint-pattern';
import { DeleteScheduleDto } from '@app/shared/assessment-engine/dto/request/delete-schedule.dto';
import { UpdateScheduleDto, ScheduleAction } from '@app/shared/assessment-engine/dto/request/update-schedule.dto';
import { ICustomRequest } from '@app/shared/dto/custom-request.dts';

import { TestScheduleStatus } from '@app/shared/assessment-engine/dto/test-schedule-status.enum';

@Injectable()
export class TestScheduleService {
  constructor(
    private readonly scheduleRepository: TestScheduleRepository,
    private readonly testRepository: TestRepository,
    private readonly notificationQueue: NotificationQueueService
  ) {}

  async scheduleTest(dto: ScheduleTestDto): Promise<BaseResponseDto> {
    const test = await this.testRepository.findOne({ id: dto.testId });
    if (!test || test.businessId !== dto.author.businessId) {
      throw new CustomException('Test not found');
    }
    let schedule = await this.scheduleRepository.findOne({
      testId: dto.testId,
      businessId: test.businessId,
    });
    if (!schedule) {
      schedule = new TestSchedule({});
      schedule.testId = dto.testId;
      schedule.businessId = test.businessId;
      schedule.startTime = new Date(dto.startTime);
      schedule.endTime = dto.endTime ? new Date(dto.endTime) : undefined;
      schedule.emails = [dto.email];
      schedule.createdBy = dto.author.email;
      schedule.status = TestScheduleStatus.ACTIVE;
      schedule = await this.scheduleRepository.createEntity(schedule);
    } else {
      if (!schedule.emails.includes(dto.email)) {
        schedule.emails.push(dto.email);
        await this.scheduleRepository.updateEntity(schedule.id, { emails: schedule.emails });
      }
    }

    await this.notificationQueue.send(EndpointPattern.NOTIFICATION_SERVICE.SEND_NOTIFICATION, {
      to: dto.email,
      subject: 'Test Scheduled',
      body: `You have been scheduled for a test. Access it at https://example.com/test/${schedule.id}. It expires on ${
        schedule.endTime ?? schedule.startTime
      }`,
    });

    return BaseResponseDto.success();
  }

  async validateSchedule(id: number, email: string): Promise<ResponseDto<{ testId: number; duration: number }>> {
    const schedule = await this.scheduleRepository.findOne({ id });
    if (!schedule) {
      throw new CustomException('Schedule not found');
    }
    if (!schedule.emails.includes(email)) {
      throw new CustomException('Email not authorized');
    }
    const now = new Date();
    if (schedule.startTime && schedule.startTime > now) {
      throw new CustomException('Test not yet available');
    }
    if (schedule.endTime && schedule.endTime < now) {
      throw new CustomException('Test expired');
    }
    const test = await this.testRepository.findOne({ id: schedule.testId });
    if (!test) {
      throw new CustomException('Test not found');
    }

    return new ResponseDto({ testId: test.id, duration: test.duration });
  }

  async getSchedules(user: ICustomRequest): Promise<ResponseDto<TestSchedule[]>> {
    const schedules = await this.scheduleRepository.findAll({
      deleted: false,
      businessId: user.businessId,
    });

    return new ResponseDto(schedules);
  }

  async updateSchedules(dto: UpdateScheduleDto): Promise<BaseResponseDto> {
    for (const id of dto.ids) {
      const schedule = await this.scheduleRepository.findOne({
        id,
        businessId: dto.author.businessId,
      });
      if (!schedule) {
        continue;
      }
      if (dto.action === ScheduleAction.RESCHEDULE) {
        schedule.startTime = new Date(dto.startTime!);
        schedule.endTime = dto.endTime ? new Date(dto.endTime) : undefined;
        await this.scheduleRepository.updateEntity(schedule.id, schedule);
        for (const email of schedule.emails) {
          await this.notificationQueue.send(EndpointPattern.NOTIFICATION_SERVICE.SEND_NOTIFICATION, {
            to: email,
            subject: 'Test Rescheduled',
            body: `Your test has been rescheduled. New start time: ${schedule.startTime}.`,
          });
        }
      } else if (dto.action === ScheduleAction.CANCEL) {
        schedule.status = TestScheduleStatus.CANCELLED;
        schedule.active = false;
        await this.scheduleRepository.updateEntity(schedule.id, schedule);
        for (const email of schedule.emails) {
          await this.notificationQueue.send(EndpointPattern.NOTIFICATION_SERVICE.SEND_NOTIFICATION, {
            to: email,
            subject: 'Test Cancelled',
            body: 'Your scheduled test has been cancelled.',
          });
        }
      }
    }
    return BaseResponseDto.success();
  }

  async deleteSchedules(dto: DeleteScheduleDto): Promise<BaseResponseDto> {
    for (const id of dto.ids) {
      const schedule = await this.scheduleRepository.findOne({
        id,
        businessId: dto.author.businessId,
      });

      if (!schedule) {
        continue;
      }
      schedule.deleted = true;
      schedule.active = false;
      await this.scheduleRepository.updateEntity(schedule.id, schedule);
    }
    return BaseResponseDto.success();
  }
}
