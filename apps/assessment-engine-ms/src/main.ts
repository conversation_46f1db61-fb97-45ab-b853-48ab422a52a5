import { NestFactory } from '@nestjs/core';
import { AssessmentEngineMsModule } from './assessment-engine-ms.module';
import { Transport } from '@nestjs/microservices';
import { Logger } from 'nestjs-pino';
import { ConfigService } from '@nestjs/config';
import { QueueName } from '@app/shared/util/QueueName';
import { RmqAckInterceptor } from '@app/shared/interceptors/rmq-exception.interceptor';

async function bootstrap() {
  const appM = await NestFactory.create(AssessmentEngineMsModule);
  const configService = appM.get(ConfigService);

  appM.useLogger(appM.get(Logger));
  appM.connectMicroservice({
    transport: Transport.TCP,
    options: {
      port: configService.get<number>('ASSESSMENT_ENGINE_MS_PORT'),
      host: '0.0.0.0',
    },
  });
  appM.connectMicroservice({
    transport: Transport.RMQ,
    options: {
      urls: [configService.get<string>('RABBITMQ_URI')],
      queue: QueueName.ASSESSMENT_ENGINE_QUEUE,
      queueOptions: {
        durable: true,
      },
      noAck: false,
      prefetchCount: 1,
    },
  });
  appM.useGlobalInterceptors(new RmqAckInterceptor());

  await appM.startAllMicroservices();
}
bootstrap();
