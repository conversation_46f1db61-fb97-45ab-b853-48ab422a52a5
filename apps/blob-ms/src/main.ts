import { NestFactory } from '@nestjs/core';
import { BlobMsModule } from './blob-ms.module';
import { Transport } from '@nestjs/microservices';

async function bootstrap() {
  const app = await NestFactory.createMicroservice(BlobMsModule, {
    transport: Transport.TCP,
    options: {
      port: process.env.BLOB_MS_PORT,
      host: '0.0.0.0',
    },
  });

  await app.listen();
  console.log(`Blob Microservice is running on port ${process.env.BLOB_MS_PORT}`);
}
bootstrap();
