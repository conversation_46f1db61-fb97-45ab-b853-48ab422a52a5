import { Logger } from 'nestjs-pino';
import { join } from 'path';
import * as fs from 'fs/promises';
import { IFileStorageService } from '../storage.interface';
import { CustomException } from '../../../../../libs/shared/src/exception/custom.exception';

export class DiskStorageService implements IFileStorageService {
  private readonly uploadPath = join(process.cwd(), 'uploads');

  constructor(private readonly logger: Logger) {
    this.ensureUploadsDirExists();
  }

  async save(
    file: { originalname: string; buffer: Buffer },
    path?: string // Path is optional for disk storage, we use a default
  ): Promise<string> {
    const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1e9);
    const filename = `${uniqueSuffix}-${file.originalname.replace(/\s/g, '_')}`;
    const fullPath = join(this.uploadPath, filename);

    try {
      await fs.writeFile(fullPath, file.buffer);
      this.logger.log(`File saved successfully to: ${fullPath}`);
      return `/uploads/${filename}`; // Return a web-accessible path
    } catch (error) {
      this.logger.error(`Failed to save file: ${filename}`, error);
      throw new Error('Could not save file to disk.');
    }
  }

  async delete(fileId: string): Promise<void> {
    const filePath = join(this.uploadPath, fileId.replace('/uploads/', ''));

    try {
      await fs.unlink(filePath);
      this.logger.log(`File deleted successfully: ${filePath}`);
    } catch (error: any) {
      if (error.code === 'ENOENT') {
        this.logger.warn(`File not found, cannot delete: ${filePath}`);
        return; // Or throw an error if you prefer
      }
      this.logger.error(`Failed to delete file: ${filePath}`, error);
      throw new CustomException('Could not delete file from disk.');
    }
  }

  async download(fileId: string): Promise<Buffer> {
    const filePath = join(this.uploadPath, fileId.replace('/uploads/', ''));

    try {
      const fileBuffer = await fs.readFile(filePath);
      this.logger.log(`File downloaded successfully: ${filePath}`);
      return fileBuffer;
    } catch (error: any) {
      this.logger.error(`Failed to download file: ${filePath}`, error);
      throw new CustomException('Could not read file from disk.');
    }
  }

  private async ensureUploadsDirExists() {
    try {
      await fs.mkdir(this.uploadPath, { recursive: true });
      this.logger.log('Uploads directory created or already exists.');
    } catch (error) {
      this.logger.error('Error creating uploads directory', error);
    }
  }
}
