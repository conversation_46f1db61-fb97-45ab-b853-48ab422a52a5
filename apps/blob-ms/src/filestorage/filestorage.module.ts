import { Module, DynamicModule } from '@nestjs/common';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { CloudStorageService } from './services/cloud.storage';
import { DiskStorageService } from './services/disk-storage.service';
import { Logger, LoggerModule } from 'nestjs-pino';
import { ApLoggerModule } from '../../../../libs/shared/src/logger/logger.module';

@Module({})
export class FileStorageModule {
  static register(): DynamicModule {
    const storageProvider = {
      provide: 'IFileStorageService', // Use a token for the interface
      useFactory: (configService: ConfigService, logger: Logger) => {
        return configService.get<string>('STORAGE_ENGINE') === 'CLOUD'
          ? new CloudStorageService(logger)
          : new DiskStorageService(logger);
      },
      inject: [ConfigService, Logger], // Inject ConfigService and Logger,
    };

    return {
      module: FileStorageModule,
      imports: [
        ApLoggerModule, // Assuming you have a logger module
        ConfigModule.forRoot({
          isGlobal: true,
          envFilePath: './.env',
        }),
      ],
      providers: [storageProvider],
      exports: [storageProvider],
    };
  }
}
