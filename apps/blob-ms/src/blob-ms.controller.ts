import { Controller, Inject } from '@nestjs/common';
import { IFileStorageService } from './filestorage/storage.interface';
import { CustomException } from '../../../libs/shared/src/exception/custom.exception';
import { ResponseDto } from '../../../libs/shared/src/dto/resp/base-response.dto';
import { MessagePattern } from '@nestjs/microservices';
import { EndpointPattern } from '../../../libs/shared/src/util/endpoint-pattern';

@Controller()
export class BlobMsController {
  constructor(
    @Inject('IFileStorageService') // Inject the service using the token
    private readonly fileStorageService: IFileStorageService
  ) {}

  @MessagePattern(EndpointPattern.BLOB_SERVICE.UPLOAD_FILE)
  async uploadFile(file: Express.Multer.File): Promise<ResponseDto<string>> {
    if (!file) {
      throw new CustomException('No file uploaded or file size exceeds limit of 5MB');
    }
    const filePath = await this.fileStorageService.save(file);
    return new ResponseDto(filePath);
  }

  @MessagePattern(EndpointPattern.BLOB_SERVICE.GET_FILE)
  async downloadFile(fileId: string): Promise<Buffer> {
    return await this.fileStorageService.download(fileId);
  }

  @MessagePattern(EndpointPattern.BLOB_SERVICE.DELETE_FILE)
  async deleteFile(fileId: string) {
    return await this.fileStorageService.delete(fileId);
  }
}
