
services:
  aptitest_postgres:
    image: postgres:14.1-alpine
    container_name: aptitest_postgres
    restart: always
    environment:
      POSTGRES_USER: ${DB_USERNAME}
      POSTGRES_PASSWORD: ${DB_PASSWORD}
      POSTGRES_DB: aptitest
    ports:
      - 5432:5432
    volumes:
      - aptitest_postgres_data:/var/lib/postgresql/data
    networks:
      - microservices-network

networks:
  microservices-network:
    driver: bridge

volumes:
  aptitest_postgres_data:
