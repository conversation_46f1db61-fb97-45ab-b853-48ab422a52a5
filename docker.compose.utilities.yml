services:
  aptitest-redis-master:
    image: redis:6.0.7
    container_name: aptitest-redis-master
    restart: always
    volumes:
      - aptitest_redis_master:/data
    ports:
      - 6379:6379
    networks:
      - microservices-network

  aptitest_redis_insight:
    image: redis/redisinsight:latest
    container_name: aptitest_redis_insight
    restart: always
    ports:
      - 5540:5540
    volumes:
      - redis_insight_volume_data:/db
    depends_on:
      - aptitest-redis-master
    networks:
      - microservices-network

  aptitest-rabbitmq:
    image: rabbitmq:3-management
    container_name: aptitest-rabbitmq
    restart: always
    environment:
      RABBITMQ_DEFAULT_USER: ${RABBITMQ_DEFAULT_USER}
      RABBITMQ_DEFAULT_PASS: ${RABBITMQ_DEFAULT_PASS}
    ports:
      - 5672:5672  
      - 15672:15672  
    volumes:
      - rabbitmq_data:/var/lib/rabbitmq
    networks:
      - microservices-network

networks:
  microservices-network:
    driver: bridge

volumes:
  aptitest_redis_master:
  rabbitmq_data:
  redis_insight_volume_data:
